// Storage Manager class
class Storage {
    constructor(app) {
        this.app = app;
    }
    
    // Get project data for saving
    getProjectData() {
        const components = this.app.componentManager.getComponents();
        const rules = this.app.automationManager.getRules();
        
        return {
            version: '1.0',
            timestamp: new Date().toISOString(),
            components,
            rules
        };
    }
    
    // Load project data from JSON
    loadProjectData(projectData) {
        if (!projectData || !projectData.components || !projectData.rules) {
            console.error('Invalid project data');
            return false;
        }
        
        // Clear existing data
        this.clearExistingData();
        
        // Load components
        projectData.components.forEach(component => {
            this.app.componentManager.addComponent(component);
        });
        
        // Load rules
        projectData.rules.forEach(rule => {
            this.app.automationManager.addRule(rule);
        });
        
        return true;
    }
    
    // Clear existing data
    clearExistingData() {
        // Clear component cards
        const componentsList = document.getElementById('components-list');
        componentsList.innerHTML = '';
        
        // Clear rule cards
        const rulesList = document.getElementById('rules-list');
        rulesList.innerHTML = '';
        
        // Reset component manager
        this.app.componentManager.components = [];
        this.app.componentManager.nextId = 1;
        
        // Reset automation manager
        this.app.automationManager.rules = [];
        this.app.automationManager.nextId = 1;
    }
} 