// Main App class
class App {
    constructor() {
        // Initialize managers
        this.componentManager = new ComponentManager(this);
        this.automationManager = new AutomationManager(this);
        this.codeGenerator = new CodeGenerator(this);
        this.wifiConfigManager = new WiFiConfigManager(this);
        this.ui = new UI(this);
        this.storage = new Storage(this);
        
        // Initialize the application
        this.init();
    }
    
    init() {
        // Initialize tab switching
        this.initTabs();
        
        // Initialize event listeners
        this.initEventListeners();

        // Initialize board selection
        this.initBoardSelection();
        
        // Apply theme preferences
        this.applyTheme();
    }
    
    initTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                
                // Remove active class from all buttons and contents
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked button and corresponding content
                btn.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
        

    }
    
    initEventListeners() {
        // Theme toggle
        const themeToggle = document.querySelector('.theme-toggle');
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            this.saveThemePreference();
        });
        
        // Generate code button
        const generateCodeBtn = document.getElementById('generate-code-btn');
        generateCodeBtn.addEventListener('click', () => {
            this.generateCode();
        });
        
        // Copy Arduino code button
        const copyArduinoBtn = document.getElementById('copy-arduino-btn');
        copyArduinoBtn.addEventListener('click', () => {
            this.copyCode('arduino');
        });
        

        
        // Download button
        const downloadBtn = document.getElementById('download-btn');
        downloadBtn.addEventListener('click', () => {
            this.downloadFiles();
        });
        
        // Save project button
        const saveProjectBtn = document.getElementById('save-project-btn');
        saveProjectBtn.addEventListener('click', () => {
            this.saveProject();
        });
        
        // Load project button
        const loadProjectBtn = document.getElementById('load-project-btn');
        loadProjectBtn.addEventListener('click', () => {
            this.loadProject();
        });
    }
    
    applyTheme() {
        const isDarkTheme = localStorage.getItem('darkTheme') === 'true';
        if (isDarkTheme) {
            document.body.classList.add('dark-theme');
        } else {
            document.body.classList.remove('dark-theme');
        }
    }
    
    saveThemePreference() {
        const isDarkTheme = document.body.classList.contains('dark-theme');
        localStorage.setItem('darkTheme', isDarkTheme);
    }
    
    generateCode() {
        const components = this.componentManager.getComponents();

        if (components.length === 0) {
            this.showNotification('Please add at least one component first', 'error');
            return;
        }

        try {
            const arduinoCode = this.codeGenerator.generateArduinoCode();
            const codeElement = document.getElementById('arduino-code');
            codeElement.textContent = arduinoCode;

            // Apply syntax highlighting
            if (window.syntaxHighlighter) {
                window.syntaxHighlighter.highlightElement(codeElement);
            }

            this.showNotification('Arduino code generated successfully');
        } catch (error) {
            console.error('Error generating code:', error);
            this.showNotification('Error generating code: ' + error.message, 'error');
        }
    }
    
    copyCode(type) {
        const codeElement = document.getElementById(`${type}-code`);
        const code = codeElement.textContent;
        
        navigator.clipboard.writeText(code)
            .then(() => {
                this.showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} code copied to clipboard`);
            })
            .catch(err => {
                console.error('Failed to copy code:', err);
                this.showNotification('Failed to copy code', 'error');
            });
    }
    
    downloadFiles() {
        const arduinoCode = document.getElementById('arduino-code').textContent;

        if (!arduinoCode || arduinoCode.trim() === '') {
            this.showNotification('Please generate Arduino code first', 'error');
            return;
        }

        // Download Arduino code
        this.downloadFile('ESP8266_Home_Automation.ino', arduinoCode);
        this.showNotification('Arduino code downloaded successfully');
    }
    
    downloadFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
        element.setAttribute('download', filename);
        
        element.style.display = 'none';
        document.body.appendChild(element);
        
        element.click();
        
        document.body.removeChild(element);
    }
    
    saveProject() {
        const projectData = this.storage.getProjectData();
        const jsonData = JSON.stringify(projectData, null, 2);
        
        this.downloadFile('esp8266_project.json', jsonData);
        this.showNotification('Project saved');
    }
    
    loadProject() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const projectData = JSON.parse(e.target.result);
                    this.storage.loadProjectData(projectData);
                    this.showNotification('Project loaded successfully');
                } catch (error) {
                    console.error('Error loading project:', error);
                    this.showNotification('Error loading project', 'error');
                }
            };
            reader.readAsText(file);
        });
        
        input.click();
    }
    
    initBoardSelection() {
        const boardSelect = document.getElementById('board-select');

        boardSelect.addEventListener('change', (e) => {
            const selectedBoard = e.target.value;
            const success = this.componentManager.setBoard(selectedBoard);

            if (success) {
                this.showNotification(`Board changed to ${selectedBoard}`);

                // Update UI to reflect available pins
                this.ui.updateComponentForms();
            } else {
                this.showNotification('Error changing board', 'error');
                // Revert selection
                boardSelect.value = this.componentManager.getSelectedBoard();
            }
        });

        // Set initial board
        boardSelect.value = this.componentManager.getSelectedBoard();
    }

    showNotification(message, type = 'success') {
        this.ui.showNotification(message, type);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
}); 