// Code Generator class
class CodeGenerator {
    constructor(app) {
        this.app = app;
    }
    
    generateArduinoCode() {
        const components = this.app.componentManager.getComponents();
        const rules = this.app.automationManager.getRules();
        
        if (components.length === 0) {
            return '// Please add at least one component to generate code';
        }
        
        let code = this.generateArduinoHeaders();
        code += this.generateArduinoVariables(components);
        code += this.generateArduinoSetup(components);
        code += this.generateArduinoLoop(components, rules);
        code += this.generateArduinoHelperFunctions(components, rules);
        
        return code;
    }
    
    generateHtmlInterface() {
        const components = this.app.componentManager.getComponents();
        
        if (components.length === 0) {
            return '<!-- Please add at least one component to generate interface -->';
        }
        
        let html = this.generateHtmlHeader();
        html += this.generateHtmlBody(components);
        html += this.generateHtmlFooter();
        
        return html;
    }
    
    // Arduino Code Generation Methods
    
    generateArduinoHeaders() {
        // Get all components to determine required libraries
        const components = this.app.componentManager.getComponents();
        
        let headers = '';
        
        // Common headers
        headers += '// ESP8266 Home Automation Generated Sketch\n';
        headers += '// Generated on ' + new Date().toLocaleString() + '\n\n';
        
        // Board-specific libraries
        const selectedBoard = this.app.componentManager.getSelectedBoard();
        if (selectedBoard === 'ESP32') {
            headers += '#include <WiFi.h>\n';
            headers += '#include <WebServer.h>\n\n';
        } else {
            headers += '#include <ESP8266WiFi.h>\n';
            headers += '#include <ESP8266WebServer.h>\n\n';
        }
        
        // Check for specific component requirements
        const hasDHT = components.some(c => c.type === 'dht11');
        const hasServo = components.some(c => c.type === 'servo');
        const hasLedStrip = components.some(c => c.type === 'led-strip');
        const hasIR = components.some(c => c.type === 'ir');
        const hasUltrasonic = components.some(c => c.type === 'ultrasonic');
        
        if (hasDHT) {
            headers += '#include <DHT.h>\n';
        }
        
        if (hasServo) {
            headers += '#include <Servo.h>\n';
        }
        
        if (hasLedStrip) {
            headers += '#include <Adafruit_NeoPixel.h>\n';
        }
        
        if (hasIR) {
            headers += '#include <IRremote.h>\n';
        }
        
        headers += '\n';
        
        // WiFi Configuration from WiFi Config Manager
        headers += this.app.wifiConfigManager.generateWiFiCode(selectedBoard);
        
        return headers;
    }
    
    generateArduinoVariables(components) {
        let variables = '// Component Definitions\n';
        
        // Process each component
        components.forEach(component => {
            const componentDef = this.app.componentManager.getComponentDefinition(component.type);
            
            variables += `// ${component.name} (${componentDef.name})\n`;
            
            switch (component.type) {
                case 'dht11':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `DHT ${this.safeVarName(component.name)}(${this.safeVarName(component.name)}_PIN, DHT11);\n`;
                    variables += `float ${this.safeVarName(component.name)}_temperature = 0;\n`;
                    variables += `float ${this.safeVarName(component.name)}_humidity = 0;\n`;
                    break;
                    
                case 'pir':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `bool ${this.safeVarName(component.name)}_motion = false;\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_lastDetection = 0;\n`;
                    break;
                    
                case 'ultrasonic':
                    variables += `#define ${this.safeVarName(component.name)}_TRIG_PIN ${component.trigPin}\n`;
                    variables += `#define ${this.safeVarName(component.name)}_ECHO_PIN ${component.echoPin}\n`;
                    variables += `long ${this.safeVarName(component.name)}_distance = 0;\n`;
                    break;
                    
                case 'ldr':
                case 'rain':
                case 'soil':
                case 'flame':
                case 'gas':
                case 'sound':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `int ${this.safeVarName(component.name)}_value = 0;\n`;
                    break;
                    
                case 'ir':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `IRrecv ${this.safeVarName(component.name)}(${this.safeVarName(component.name)}_PIN);\n`;
                    variables += `decode_results ${this.safeVarName(component.name)}_results;\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_code = 0;\n`;
                    break;
                    
                case 'relay':
                case 'buzzer':
                case 'water-pump':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `bool ${this.safeVarName(component.name)}_state = ${component.initialState === 'HIGH' ? 'true' : 'false'};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_timer = 0;\n`;
                    break;
                    
                case 'pwm':
                case 'fan':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `int ${this.safeVarName(component.name)}_value = ${component.initialValue};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_timer = 0;\n`;
                    break;
                    
                case 'servo':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `Servo ${this.safeVarName(component.name)};\n`;
                    variables += `int ${this.safeVarName(component.name)}_angle = ${component.initialAngle};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_timer = 0;\n`;
                    break;
                    
                case 'led-strip':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.dataPin}\n`;
                    variables += `#define ${this.safeVarName(component.name)}_NUM_LEDS ${component.numLeds}\n`;
                    variables += `Adafruit_NeoPixel ${this.safeVarName(component.name)} = Adafruit_NeoPixel(${this.safeVarName(component.name)}_NUM_LEDS, ${this.safeVarName(component.name)}_PIN, NEO_GRB + NEO_KHZ800);\n`;
                    variables += `int ${this.safeVarName(component.name)}_brightness = 50;\n`;
                    variables += `uint32_t ${this.safeVarName(component.name)}_color = 0xFFFFFF;\n`;
                    variables += `bool ${this.safeVarName(component.name)}_state = false;\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_timer = 0;\n`;
                    break;

                // Digital sensors
                case 'button':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `bool ${this.safeVarName(component.name)}_pressed = false;\n`;
                    variables += `bool ${this.safeVarName(component.name)}_lastState = ${component.pullup ? 'HIGH' : 'LOW'};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_lastDebounce = 0;\n`;
                    variables += `const unsigned long ${this.safeVarName(component.name)}_debounceDelay = 50;\n`;
                    break;

                case 'switch':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `bool ${this.safeVarName(component.name)}_state = ${component.pullup ? 'false' : 'true'};\n`;
                    variables += `bool ${this.safeVarName(component.name)}_lastReading = ${component.pullup ? 'HIGH' : 'LOW'};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_lastDebounce = 0;\n`;
                    variables += `const unsigned long ${this.safeVarName(component.name)}_debounceDelay = 50;\n`;
                    break;

                case 'magnetic':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `bool ${this.safeVarName(component.name)}_doorOpen = ${component.pullup ? 'false' : 'true'};\n`;
                    variables += `bool ${this.safeVarName(component.name)}_lastReading = ${component.pullup ? 'HIGH' : 'LOW'};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_lastDebounce = 0;\n`;
                    variables += `const unsigned long ${this.safeVarName(component.name)}_debounceDelay = 50;\n`;
                    break;

                case 'vibration':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `bool ${this.safeVarName(component.name)}_vibration = false;\n`;
                    variables += `bool ${this.safeVarName(component.name)}_lastReading = ${component.pullup ? 'HIGH' : 'LOW'};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_lastDetection = 0;\n`;
                    break;

                case 'tilt':
                    variables += `#define ${this.safeVarName(component.name)}_PIN ${component.pin}\n`;
                    variables += `bool ${this.safeVarName(component.name)}_tilted = ${component.pullup ? 'false' : 'true'};\n`;
                    variables += `bool ${this.safeVarName(component.name)}_lastReading = ${component.pullup ? 'HIGH' : 'LOW'};\n`;
                    variables += `unsigned long ${this.safeVarName(component.name)}_lastDebounce = 0;\n`;
                    variables += `const unsigned long ${this.safeVarName(component.name)}_debounceDelay = 50;\n`;
                    break;
            }
            
            variables += '\n';
        });
        
        // Add variables for automation rules timers
        const rules = this.app.automationManager.getRules();
        if (rules.length > 0) {
            variables += '// Automation Rule Timers\n';
            
            rules.forEach(rule => {
                rule.actions.forEach((action, index) => {
                    if (action.duration) {
                        variables += `unsigned long rule${rule.id}_action${index}_startTime = 0;\n`;
                        variables += `bool rule${rule.id}_action${index}_active = false;\n`;
                    }
                });
            });
            
            variables += '\n';
        }
        
        // General timers
        variables += '// General Timers\n';
        variables += 'unsigned long lastSensorRead = 0;\n';
        variables += 'const long sensorReadInterval = 1000; // Read sensors every 1 second\n\n';
        
        return variables;
    }
    
    generateArduinoSetup(components) {
        let setup = 'void setup() {\n';
        
        // Serial monitor
        setup += '  // Initialize Serial Monitor\n';
        setup += '  Serial.begin(115200);\n';
        setup += '  Serial.println("ESP8266 Home Automation");\n\n';
        
        // WiFi setup from WiFi Config Manager
        setup += this.app.wifiConfigManager.generateWiFiSetupCode();
        
        // Component initialization
        setup += '  // Initialize Components\n';
        components.forEach(component => {
            const componentDef = this.app.componentManager.getComponentDefinition(component.type);
            
            setup += `  // ${component.name} (${componentDef.name})\n`;
            
            switch (component.type) {
                case 'dht11':
                    setup += `  ${this.safeVarName(component.name)}.begin();\n`;
                    break;
                    
                case 'pir':
                    setup += `  pinMode(${this.safeVarName(component.name)}_PIN, INPUT);\n`;
                    break;
                    
                case 'ultrasonic':
                    setup += `  pinMode(${this.safeVarName(component.name)}_TRIG_PIN, OUTPUT);\n`;
                    setup += `  pinMode(${this.safeVarName(component.name)}_ECHO_PIN, INPUT);\n`;
                    break;
                    
                case 'ldr':
                case 'rain':
                case 'soil':
                case 'flame':
                case 'gas':
                case 'sound':
                    // Analog pins don't need pinMode
                    if (component.pin !== 'A0') {
                        setup += `  pinMode(${this.safeVarName(component.name)}_PIN, INPUT);\n`;
                    }
                    break;
                    
                case 'ir':
                    setup += `  ${this.safeVarName(component.name)}.enableIRIn();\n`;
                    break;
                    
                case 'relay':
                case 'buzzer':
                case 'water-pump':
                    setup += `  pinMode(${this.safeVarName(component.name)}_PIN, OUTPUT);\n`;
                    setup += `  digitalWrite(${this.safeVarName(component.name)}_PIN, ${component.initialState});\n`;
                    break;
                    
                case 'pwm':
                case 'fan':
                    setup += `  pinMode(${this.safeVarName(component.name)}_PIN, OUTPUT);\n`;
                    setup += `  analogWrite(${this.safeVarName(component.name)}_PIN, ${component.initialValue});\n`;
                    break;
                    
                case 'servo':
                    setup += `  ${this.safeVarName(component.name)}.attach(${this.safeVarName(component.name)}_PIN);\n`;
                    setup += `  ${this.safeVarName(component.name)}.write(${component.initialAngle});\n`;
                    break;
                    
                case 'led-strip':
                    setup += `  ${this.safeVarName(component.name)}.begin();\n`;
                    setup += `  ${this.safeVarName(component.name)}.show();\n`;
                    break;

                // Digital sensors
                case 'button':
                case 'switch':
                case 'magnetic':
                case 'tilt':
                    setup += `  pinMode(${this.safeVarName(component.name)}_PIN, ${component.pullup ? 'INPUT_PULLUP' : 'INPUT'});\n`;
                    break;

                case 'vibration':
                    setup += `  pinMode(${this.safeVarName(component.name)}_PIN, ${component.pullup ? 'INPUT_PULLUP' : 'INPUT'});\n`;
                    break;
            }
            
            setup += '\n';
        });
        
        // Web server routes
        setup += '  // Setup Web Server Routes\n';
        setup += '  server.on("/", handleRoot);\n';
        setup += '  server.on("/data", handleData);\n';
        
        // Generate device control routes
        const outputDevices = components.filter(c => {
            const def = this.app.componentManager.getComponentDefinition(c.type);
            return def.type === 'output';
        });
        
        if (outputDevices.length > 0) {
            outputDevices.forEach(device => {
                setup += `  server.on("/control/${this.safeVarName(device.name)}", handle${this.capitalizeFirst(this.safeVarName(device.name))}Control);\n`;
            });
        }
        
        setup += '\n';
        setup += '  // Start the server\n';
        setup += '  server.begin();\n';
        setup += '  Serial.println("HTTP server started");\n';
        setup += '}\n\n';
        
        return setup;
    }
    
    generateArduinoLoop(components, rules) {
        let loop = 'void loop() {\n';
        
        // Handle client requests
        loop += '  // Handle client requests\n';
        loop += '  server.handleClient();\n\n';
        
        // Sensor readings
        loop += '  // Periodic sensor readings\n';
        loop += '  unsigned long currentMillis = millis();\n';
        loop += '  if (currentMillis - lastSensorRead >= sensorReadInterval) {\n';
        loop += '    lastSensorRead = currentMillis;\n';
        loop += '    readSensors();\n';
        loop += '  }\n\n';
        
        // Automation rules
        if (rules.length > 0) {
            loop += '  // Check automation rules\n';
            loop += '  checkAutomationRules();\n\n';
        }
        
        // Timed action checkers
        const hasTimedActions = rules.some(rule => 
            rule.actions.some(action => action.duration)
        );
        
        if (hasTimedActions) {
            loop += '  // Check timed actions\n';
            loop += '  checkTimedActions();\n';
        }
        
        loop += '}\n\n';
        
        return loop;
    }
    
    generateArduinoHelperFunctions(components, rules) {
        let functions = '';
        
        // Read sensors function
        functions += this.generateReadSensorsFunction(components);
        
        // Automation rules function
        if (rules.length > 0) {
            functions += this.generateCheckAutomationRulesFunction(rules);
            
            // Time-based action handler
            const hasTimedActions = rules.some(rule => 
                rule.actions.some(action => action.duration)
            );
            
            if (hasTimedActions) {
                functions += this.generateCheckTimedActionsFunction(rules);
            }
        }
        
        // Web handlers
        functions += this.generateWebHandlers(components);
        
        // Device control handlers
        functions += this.generateDeviceControlHandlers(components);
        
        return functions;
    }
    
    generateReadSensorsFunction(components) {
        const sensors = components.filter(c => {
            const componentDef = this.app.componentManager.getComponentDefinition(c.type);
            return componentDef.type === 'sensor';
        });
        
        if (sensors.length === 0) {
            return '';
        }
        
        let function_code = '// Read sensor values\n';
        function_code += 'void readSensors() {\n';
        
        sensors.forEach(sensor => {
            function_code += `  // Read ${sensor.name}\n`;
            
            switch (sensor.type) {
                case 'dht11':
                    function_code += `  ${this.safeVarName(sensor.name)}_temperature = ${this.safeVarName(sensor.name)}.readTemperature();\n`;
                    function_code += `  ${this.safeVarName(sensor.name)}_humidity = ${this.safeVarName(sensor.name)}.readHumidity();\n`;
                    break;
                    
                case 'pir':
                    function_code += `  ${this.safeVarName(sensor.name)}_motion = digitalRead(${this.safeVarName(sensor.name)}_PIN) == HIGH;\n`;
                    function_code += `  if (${this.safeVarName(sensor.name)}_motion) {\n`;
                    function_code += `    ${this.safeVarName(sensor.name)}_lastDetection = millis();\n`;
                    function_code += `  }\n`;
                    break;
                    
                case 'ultrasonic':
                    function_code += `  // Trigger ultrasonic sensor\n`;
                    function_code += `  digitalWrite(${this.safeVarName(sensor.name)}_TRIG_PIN, LOW);\n`;
                    function_code += `  delayMicroseconds(2);\n`;
                    function_code += `  digitalWrite(${this.safeVarName(sensor.name)}_TRIG_PIN, HIGH);\n`;
                    function_code += `  delayMicroseconds(10);\n`;
                    function_code += `  digitalWrite(${this.safeVarName(sensor.name)}_TRIG_PIN, LOW);\n\n`;
                    function_code += `  // Read echo time\n`;
                    function_code += `  long duration = pulseIn(${this.safeVarName(sensor.name)}_ECHO_PIN, HIGH);\n`;
                    function_code += `  // Calculate distance\n`;
                    function_code += `  ${this.safeVarName(sensor.name)}_distance = duration * 0.034 / 2; // Speed of sound wave divided by 2 (go and back)\n`;
                    break;
                    
                case 'ldr':
                case 'rain':
                case 'soil':
                case 'flame':
                case 'gas':
                case 'sound':
                    if (sensor.pin === 'A0') {
                        function_code += `  ${this.safeVarName(sensor.name)}_value = analogRead(A0);\n`;
                    } else {
                        function_code += `  ${this.safeVarName(sensor.name)}_value = digitalRead(${this.safeVarName(sensor.name)}_PIN);\n`;
                    }
                    break;
                    
                case 'ir':
                    function_code += `  if (${this.safeVarName(sensor.name)}.decode(&${this.safeVarName(sensor.name)}_results)) {\n`;
                    function_code += `    ${this.safeVarName(sensor.name)}_code = ${this.safeVarName(sensor.name)}_results.value;\n`;
                    function_code += `    ${this.safeVarName(sensor.name)}.resume();\n`;
                    function_code += `  }\n`;
                    break;
            }
            
            function_code += '\n';
        });
        
        function_code += '}\n\n';
        
        return function_code;
    }
    
    generateCheckAutomationRulesFunction(rules) {
        if (rules.length === 0) {
            return '';
        }
        
        let function_code = '// Check automation rules\n';
        function_code += 'void checkAutomationRules() {\n';
        
        rules.forEach(rule => {
            const sensor = this.app.componentManager.getComponentById(rule.condition.sensorId);
            if (!sensor) return;
            
            // Start rule condition check
            function_code += `  // Rule ${rule.id}\n`;
            
            // Get sensor reading value based on type
            let sensorValue = '';
            
            switch (rule.condition.reading) {
                case 'temperature':
                    sensorValue = `${this.safeVarName(sensor.name)}_temperature`;
                    break;
                    
                case 'humidity':
                    sensorValue = `${this.safeVarName(sensor.name)}_humidity`;
                    break;
                    
                case 'motion':
                    sensorValue = `${this.safeVarName(sensor.name)}_motion ? 1 : 0`;
                    break;
                    
                case 'distance':
                    sensorValue = `${this.safeVarName(sensor.name)}_distance`;
                    break;
                    
                case 'rainLevel':
                case 'lightLevel':
                case 'moistureLevel':
                case 'flameLevel':
                case 'gasLevel':
                case 'soundLevel':
                case 'irCode':
                    sensorValue = `${this.safeVarName(sensor.name)}_value`;
                    break;
            }
            
            // Build condition based on operator
            let condition = '';
            
            switch (rule.condition.operator) {
                case 'gt':
                    condition = `${sensorValue} > ${rule.condition.value}`;
                    break;
                    
                case 'lt':
                    condition = `${sensorValue} < ${rule.condition.value}`;
                    break;
                    
                case 'eq':
                    condition = `${sensorValue} == ${rule.condition.value}`;
                    break;
            }
            
            function_code += `  if (${condition}) {\n`;
            
            // Add actions
            rule.actions.forEach((action, index) => {
                const device = this.app.componentManager.getComponentById(action.deviceId);
                if (!device) return;
                
                function_code += `    // Action ${index + 1}: ${device.name}\n`;
                
                switch (action.type) {
                    case 'on':
                        if (device.type === 'relay' || device.type === 'buzzer' || device.type === 'water-pump') {
                            function_code += `    ${this.safeVarName(device.name)}_state = true;\n`;
                            function_code += `    digitalWrite(${this.safeVarName(device.name)}_PIN, HIGH);\n`;
                        } else if (device.type === 'led-strip') {
                            function_code += `    ${this.safeVarName(device.name)}_state = true;\n`;
                            function_code += `    ${this.safeVarName(device.name)}.setBrightness(${this.safeVarName(device.name)}_brightness);\n`;
                            function_code += `    for (int i = 0; i < ${this.safeVarName(device.name)}_NUM_LEDS; i++) {\n`;
                            function_code += `      ${this.safeVarName(device.name)}.setPixelColor(i, ${this.safeVarName(device.name)}_color);\n`;
                            function_code += `    }\n`;
                            function_code += `    ${this.safeVarName(device.name)}.show();\n`;
                        }
                        break;
                        
                    case 'off':
                        if (device.type === 'relay' || device.type === 'buzzer' || device.type === 'water-pump') {
                            function_code += `    ${this.safeVarName(device.name)}_state = false;\n`;
                            function_code += `    digitalWrite(${this.safeVarName(device.name)}_PIN, LOW);\n`;
                        } else if (device.type === 'led-strip') {
                            function_code += `    ${this.safeVarName(device.name)}_state = false;\n`;
                            function_code += `    ${this.safeVarName(device.name)}.clear();\n`;
                            function_code += `    ${this.safeVarName(device.name)}.show();\n`;
                        }
                        break;
                        
                    case 'setValue':
                        if (device.type === 'pwm' || device.type === 'fan') {
                            function_code += `    ${this.safeVarName(device.name)}_value = ${action.value};\n`;
                            function_code += `    analogWrite(${this.safeVarName(device.name)}_PIN, ${action.value});\n`;
                        } else if (device.type === 'servo') {
                            function_code += `    ${this.safeVarName(device.name)}_angle = ${action.value};\n`;
                            function_code += `    ${this.safeVarName(device.name)}.write(${action.value});\n`;
                        } else if (device.type === 'led-strip') {
                            function_code += `    ${this.safeVarName(device.name)}_brightness = ${action.value};\n`;
                            function_code += `    ${this.safeVarName(device.name)}.setBrightness(${action.value});\n`;
                            function_code += `    ${this.safeVarName(device.name)}.show();\n`;
                        }
                        break;
                }
                
                // If action has duration, set timer
                if (action.duration) {
                    let durationMs = action.duration.value;
                    
                    // Convert to milliseconds based on unit
                    if (action.duration.unit === 'minutes') {
                        durationMs *= 60000;
                    } else if (action.duration.unit === 'hours') {
                        durationMs *= 3600000;
                    } else {
                        durationMs *= 1000; // seconds to milliseconds
                    }
                    
                    function_code += `    rule${rule.id}_action${index}_startTime = millis();\n`;
                    function_code += `    rule${rule.id}_action${index}_active = true;\n`;
                }
            });
            
            function_code += '  }\n\n';
        });
        
        function_code += '}\n\n';
        
        return function_code;
    }
    
    // HTML Generation Methods
    
    generateHtmlHeader() {
        let header = '<!DOCTYPE html>\n';
        header += '<html lang="en">\n';
        header += '<head>\n';
        header += '    <meta charset="UTF-8">\n';
        header += '    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n';
        header += '    <title>ESP8266 Home Automation</title>\n';
        header += '    <style>\n';
        header += '        body { font-family: Arial, Helvetica, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }\n';
        header += '        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }\n';
        header += '        header { background-color: #2196F3; color: white; padding: 15px; text-align: center; }\n';
        header += '        h1 { margin: 0; }\n';
        header += '        .card { background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden; }\n';
        header += '        .card-header { background-color: #f1f1f1; padding: 12px 20px; border-bottom: 1px solid #ddd; font-weight: bold; }\n';
        header += '        .card-body { padding: 20px; }\n';
        header += '        .dashboard { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; }\n';
        header += '        .sensor-value { font-size: 24px; margin: 10px 0; }\n';
        header += '        .control-btn { background-color: #4CAF50; border: none; color: white; padding: 10px 20px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer; border-radius: 4px; }\n';
        header += '        .control-btn.off { background-color: #f44336; }\n';
        header += '        .slider { width: 100%; margin: 10px 0; }\n';
        header += '        @media (max-width: 600px) { .dashboard { grid-template-columns: 1fr; } }\n';
        header += '    </style>\n';
        header += '</head>\n';
        header += '<body>\n';
        header += '    <header>\n';
        header += '        <h1>ESP8266 Home Automation</h1>\n';
        header += '    </header>\n';
        header += '    <div class="container">\n';
        header += '        <div class="dashboard">\n';
        
        return header;
    }
    
    generateHtmlBody(components) {
        let body = '';
        
        // Process each component
        components.forEach(component => {
            const componentDef = this.app.componentManager.getComponentDefinition(component.type);
            const safeVarName = this.safeVarName(component.name);
            
            body += `            <div class="card">\n`;
            body += `                <div class="card-header">${component.name}</div>\n`;
            body += `                <div class="card-body">\n`;
            
            switch (component.type) {
                case 'dht11':
                    body += `                    <div><span>Temperature: </span><span class="sensor-value" id="${safeVarName}_temp">-</span> °C</div>\n`;
                    body += `                    <div><span>Humidity: </span><span class="sensor-value" id="${safeVarName}_humidity">-</span> %</div>\n`;
                    break;
                    
                case 'pir':
                    body += `                    <div><span>Motion: </span><span class="sensor-value" id="${safeVarName}_motion">-</span></div>\n`;
                    break;
                    
                case 'ultrasonic':
                    body += `                    <div><span>Distance: </span><span class="sensor-value" id="${safeVarName}_distance">-</span> cm</div>\n`;
                    break;
                    
                case 'ldr':
                    body += `                    <div><span>Light Level: </span><span class="sensor-value" id="${safeVarName}_value">-</span></div>\n`;
                    break;
                    
                case 'rain':
                    body += `                    <div><span>Rain Level: </span><span class="sensor-value" id="${safeVarName}_value">-</span></div>\n`;
                    break;
                    
                case 'soil':
                    body += `                    <div><span>Soil Moisture: </span><span class="sensor-value" id="${safeVarName}_value">-</span></div>\n`;
                    break;
                    
                case 'flame':
                    body += `                    <div><span>Flame Detected: </span><span class="sensor-value" id="${safeVarName}_value">-</span></div>\n`;
                    break;
                    
                case 'gas':
                    body += `                    <div><span>Gas Level: </span><span class="sensor-value" id="${safeVarName}_value">-</span></div>\n`;
                    break;
                    
                case 'sound':
                    body += `                    <div><span>Sound Level: </span><span class="sensor-value" id="${safeVarName}_value">-</span></div>\n`;
                    break;
                    
                case 'ir':
                    body += `                    <div><span>IR Code: </span><span class="sensor-value" id="${safeVarName}_code">-</span></div>\n`;
                    break;
                    
                case 'relay':
                case 'buzzer':
                case 'water-pump':
                    body += `                    <button class="control-btn" id="${safeVarName}_btn" onclick="toggleDevice('${safeVarName}')">Turn ${component.initialState === 'HIGH' ? 'OFF' : 'ON'}</button>\n`;
                    break;
                    
                case 'pwm':
                case 'fan':
                    body += `                    <div><span>Value: </span><span class="sensor-value" id="${safeVarName}_value">${component.initialValue}</span></div>\n`;
                    body += `                    <input type="range" min="0" max="1023" value="${component.initialValue}" class="slider" id="${safeVarName}_slider" onchange="setDeviceValue('${safeVarName}', this.value)">\n`;
                    break;
                    
                case 'servo':
                    body += `                    <div><span>Angle: </span><span class="sensor-value" id="${safeVarName}_angle">${component.initialAngle}</span> °</div>\n`;
                    body += `                    <input type="range" min="0" max="180" value="${component.initialAngle}" class="slider" id="${safeVarName}_slider" onchange="setDeviceValue('${safeVarName}', this.value)">\n`;
                    break;
                    
                case 'led-strip':
                    body += `                    <button class="control-btn" id="${safeVarName}_btn" onclick="toggleDevice('${safeVarName}')">Turn ON</button>\n`;
                    body += `                    <div><span>Brightness: </span><span class="sensor-value" id="${safeVarName}_brightness">50</span> %</div>\n`;
                    body += `                    <input type="range" min="0" max="255" value="50" class="slider" id="${safeVarName}_brightness_slider" onchange="setDeviceValue('${safeVarName}', this.value)">\n`;
                    break;
            }
            
            body += `                </div>\n`;
            body += `            </div>\n`;
        });
        
        return body;
    }
    
    generateHtmlFooter() {
        let footer = '';
        footer += '        </div>\n';
        footer += '    </div>\n';
        footer += '    <script>\n';
        footer += '        // Function to update data from server\n';
        footer += '        function updateData() {\n';
        footer += '            fetch("/data")\n';
        footer += '                .then(response => response.json())\n';
        footer += '                .then(data => updateUI(data))\n';
        footer += '                .catch(error => console.error("Error fetching data:", error));\n';
        footer += '        }\n';
        footer += '\n';
        footer += '        // Update UI with data\n';
        footer += '        function updateUI(data) {\n';
        footer += '            // Update each component\n';
        footer += '            for (const key in data) {\n';
        footer += '                const element = document.getElementById(key);\n';
        footer += '                if (element) {\n';
        footer += '                    if (key.endsWith("_motion")) {\n';
        footer += '                        element.textContent = data[key] ? "Detected" : "None";\n';
        footer += '                    } else if (key.endsWith("_btn")) {\n';
        footer += '                        const deviceName = key.replace("_btn", "");\n';
        footer += '                        const stateKey = deviceName + "_state";\n';
        footer += '                        if (data[stateKey] !== undefined) {\n';
        footer += '                            element.textContent = data[stateKey] ? "Turn OFF" : "Turn ON";\n';
        footer += '                            element.className = data[stateKey] ? "control-btn off" : "control-btn";\n';
        footer += '                        }\n';
        footer += '                    } else {\n';
        footer += '                        element.textContent = data[key];\n';
        footer += '                    }\n';
        footer += '                }\n';
        footer += '            }\n';
        footer += '        }\n';
        footer += '\n';
        footer += '        // Toggle device state\n';
        footer += '        function toggleDevice(deviceName) {\n';
        footer += '            fetch(`/control?device=${deviceName}&action=toggle`)\n';
        footer += '                .then(response => response.json())\n';
        footer += '                .then(data => updateUI(data))\n';
        footer += '                .catch(error => console.error("Error toggling device:", error));\n';
        footer += '        }\n';
        footer += '\n';
        footer += '        // Set device value\n';
        footer += '        function setDeviceValue(deviceName, value) {\n';
        footer += '            fetch(`/control?device=${deviceName}&action=setValue&value=${value}`)\n';
        footer += '                .then(response => response.json())\n';
        footer += '                .then(data => updateUI(data))\n';
        footer += '                .catch(error => console.error("Error setting device value:", error));\n';
        footer += '        }\n';
        footer += '\n';
        footer += '        // Update data every 2 seconds\n';
        footer += '        setInterval(updateData, 2000);\n';
        footer += '\n';
        footer += '        // Initial update\n';
        footer += '        updateData();\n';
        footer += '    </script>\n';
        footer += '</body>\n';
        footer += '</html>';
        
        return footer;
    }
    
    generateCheckTimedActionsFunction(rules) {
        let function_code = '// Check timed actions\n';
        function_code += 'void checkTimedActions() {\n';
        function_code += '  unsigned long currentMillis = millis();\n\n';

        rules.forEach(rule => {
            rule.actions.forEach((action, index) => {
                if (action.duration) {
                    const device = this.app.componentManager.getComponentById(action.deviceId);
                    if (!device) return;

                    let durationMs = action.duration.value;

                    // Convert to milliseconds based on unit
                    if (action.duration.unit === 'minutes') {
                        durationMs *= 60000;
                    } else if (action.duration.unit === 'hours') {
                        durationMs *= 3600000;
                    } else {
                        durationMs *= 1000; // seconds to milliseconds
                    }

                    function_code += `  // Check rule ${rule.id} action ${index + 1} timer\n`;
                    function_code += `  if (rule${rule.id}_action${index}_active && (currentMillis - rule${rule.id}_action${index}_startTime >= ${durationMs})) {\n`;
                    function_code += `    rule${rule.id}_action${index}_active = false;\n`;

                    // Turn off the device after duration
                    if (device.type === 'relay' || device.type === 'buzzer' || device.type === 'water-pump') {
                        function_code += `    ${this.safeVarName(device.name)}_state = false;\n`;
                        function_code += `    digitalWrite(${this.safeVarName(device.name)}_PIN, LOW);\n`;
                    } else if (device.type === 'led-strip') {
                        function_code += `    ${this.safeVarName(device.name)}_state = false;\n`;
                        function_code += `    ${this.safeVarName(device.name)}.clear();\n`;
                        function_code += `    ${this.safeVarName(device.name)}.show();\n`;
                    }

                    function_code += '  }\n\n';
                }
            });
        });

        function_code += '}\n\n';

        return function_code;
    }

    generateWebHandlers(components) {
        let handlers = '';

        // Root handler
        handlers += '// Handle root page\n';
        handlers += 'void handleRoot() {\n';
        handlers += '  String html = "<!DOCTYPE html>";\n';
        handlers += '  html += "<html><head><title>ESP8266 Home Automation</title>";\n';
        handlers += '  html += "<meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1\\">";\n';
        handlers += '  html += "<style>";\n';
        handlers += '  html += "body{font-family:Arial;margin:0;padding:20px;background:#f4f4f4;}";\n';
        handlers += '  html += ".container{max-width:800px;margin:0 auto;}";\n';
        handlers += '  html += ".card{background:white;padding:20px;margin:10px 0;border-radius:5px;box-shadow:0 2px 5px rgba(0,0,0,0.1);}";\n';
        handlers += '  html += ".btn{background:#4CAF50;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;margin:5px;}";\n';
        handlers += '  html += ".btn.off{background:#f44336;}";\n';
        handlers += '  html += ".slider{width:100%;margin:10px 0;}";\n';
        handlers += '  html += ".sensor-value{font-size:24px;font-weight:bold;color:#2196F3;}";\n';
        handlers += '  html += "</style></head><body>";\n';
        handlers += '  html += "<div class=\\"container\\"><h1>ESP8266 Home Automation</h1>";\n';

        // Add sensor displays
        const sensors = components.filter(c => {
            const componentDef = this.app.componentManager.getComponentDefinition(c.type);
            return componentDef.type === 'sensor';
        });

        if (sensors.length > 0) {
            handlers += '  html += "<div class=\\"card\\"><h2>Sensors</h2>";\n';

            sensors.forEach(sensor => {
                const safeVarName = this.safeVarName(sensor.name);

                switch (sensor.type) {
                    case 'dht11':
                        handlers += `  html += "<p>${sensor.name} Temperature: <span class=\\"sensor-value\\" id=\\"${safeVarName}_temp\\">"; html += String(${safeVarName}_temperature); html += " °C</span></p>";\n`;
                        handlers += `  html += "<p>${sensor.name} Humidity: <span class=\\"sensor-value\\" id=\\"${safeVarName}_humidity\\">"; html += String(${safeVarName}_humidity); html += " %</span></p>";\n`;
                        break;

                    case 'pir':
                        handlers += `  html += "<p>${sensor.name} Motion: <span class=\\"sensor-value\\">"; html += (${safeVarName}_motion ? "Detected" : "None"); html += "</span></p>";\n`;
                        break;

                    case 'ultrasonic':
                        handlers += `  html += "<p>${sensor.name} Distance: <span class=\\"sensor-value\\">"; html += String(${safeVarName}_distance); html += " cm</span></p>";\n`;
                        break;

                    default:
                        handlers += `  html += "<p>${sensor.name}: <span class=\\"sensor-value\\">"; html += String(${safeVarName}_value); html += "</span></p>";\n`;
                        break;
                }
            });

            handlers += '  html += "</div>";\n';
        }

        // Add device controls
        const outputDevices = components.filter(c => {
            const componentDef = this.app.componentManager.getComponentDefinition(c.type);
            return componentDef.type === 'output';
        });

        if (outputDevices.length > 0) {
            handlers += '  html += "<div class=\\"card\\"><h2>Controls</h2>";\n';

            outputDevices.forEach(device => {
                const safeVarName = this.safeVarName(device.name);

                switch (device.type) {
                    case 'relay':
                    case 'buzzer':
                    case 'water-pump':
                        handlers += `  html += "<p>${device.name}: <button class=\\"btn"; if(${safeVarName}_state) html += " off"; html += "\\" onclick=\\"location.href='/control/${safeVarName}'\\">";\n`;
                        handlers += `  html += (${safeVarName}_state ? "Turn OFF" : "Turn ON"); html += "</button></p>";\n`;
                        break;

                    case 'pwm':
                    case 'fan':
                        handlers += `  html += "<p>${device.name} Value: <span class=\\"sensor-value\\">"; html += String(${safeVarName}_value); html += "</span></p>";\n`;
                        handlers += `  html += "<input type=\\"range\\" min=\\"0\\" max=\\"1023\\" value=\\""; html += String(${safeVarName}_value); html += "\\" class=\\"slider\\" onchange=\\"location.href='/control/${safeVarName}?value='+this.value\\">";\n`;
                        break;

                    case 'servo':
                        handlers += `  html += "<p>${device.name} Angle: <span class=\\"sensor-value\\">"; html += String(${safeVarName}_angle); html += "°</span></p>";\n`;
                        handlers += `  html += "<input type=\\"range\\" min=\\"0\\" max=\\"180\\" value=\\""; html += String(${safeVarName}_angle); html += "\\" class=\\"slider\\" onchange=\\"location.href='/control/${safeVarName}?value='+this.value\\">";\n`;
                        break;

                    case 'led-strip':
                        handlers += `  html += "<p>${device.name}: <button class=\\"btn"; if(${safeVarName}_state) html += " off"; html += "\\" onclick=\\"location.href='/control/${safeVarName}'\\">";\n`;
                        handlers += `  html += (${safeVarName}_state ? "Turn OFF" : "Turn ON"); html += "</button></p>";\n`;
                        handlers += `  html += "<p>Brightness: <span class=\\"sensor-value\\">"; html += String(${safeVarName}_brightness); html += "</span></p>";\n`;
                        handlers += `  html += "<input type=\\"range\\" min=\\"0\\" max=\\"255\\" value=\\""; html += String(${safeVarName}_brightness); html += "\\" class=\\"slider\\" onchange=\\"location.href='/control/${safeVarName}?brightness='+this.value\\">";\n`;
                        break;
                }
            });

            handlers += '  html += "</div>";\n';
        }

        handlers += '  html += "<div class=\\"card\\"><p><a href=\\"/\\">Refresh</a></p></div>";\n';
        handlers += '  html += "</div></body></html>";\n';
        handlers += '  server.send(200, "text/html", html);\n';
        handlers += '}\n\n';

        // Data handler for JSON API
        handlers += '// Handle data request\n';
        handlers += 'void handleData() {\n';
        handlers += '  String json = "{";\n';

        let jsonFields = [];

        // Add sensor data
        sensors.forEach(sensor => {
            const safeVarName = this.safeVarName(sensor.name);

            switch (sensor.type) {
                case 'dht11':
                    jsonFields.push(`"${safeVarName}_temp":" + String(${safeVarName}_temperature)`);
                    jsonFields.push(`"${safeVarName}_humidity":" + String(${safeVarName}_humidity)`);
                    break;

                case 'pir':
                    jsonFields.push(`"${safeVarName}_motion":" + String(${safeVarName}_motion ? 1 : 0)`);
                    break;

                case 'ultrasonic':
                    jsonFields.push(`"${safeVarName}_distance":" + String(${safeVarName}_distance)`);
                    break;

                default:
                    jsonFields.push(`"${safeVarName}_value":" + String(${safeVarName}_value)`);
                    break;
            }
        });

        // Add device states
        outputDevices.forEach(device => {
            const safeVarName = this.safeVarName(device.name);

            switch (device.type) {
                case 'relay':
                case 'buzzer':
                case 'water-pump':
                case 'led-strip':
                    jsonFields.push(`"${safeVarName}_state":" + String(${safeVarName}_state ? 1 : 0)`);
                    break;

                case 'pwm':
                case 'fan':
                    jsonFields.push(`"${safeVarName}_value":" + String(${safeVarName}_value)`);
                    break;

                case 'servo':
                    jsonFields.push(`"${safeVarName}_angle":" + String(${safeVarName}_angle)`);
                    break;
            }

            if (device.type === 'led-strip') {
                jsonFields.push(`"${safeVarName}_brightness":" + String(${safeVarName}_brightness)`);
            }
        });

        if (jsonFields.length > 0) {
            handlers += `  json += ${jsonFields.join(' + "," + ')};\n`;
        }

        handlers += '  json += "}";\n';
        handlers += '  server.send(200, "application/json", json);\n';
        handlers += '}\n\n';

        return handlers;
    }

    generateDeviceControlHandlers(components) {
        let handlers = '';

        const outputDevices = components.filter(c => {
            const componentDef = this.app.componentManager.getComponentDefinition(c.type);
            return componentDef.type === 'output';
        });

        outputDevices.forEach(device => {
            const safeVarName = this.safeVarName(device.name);
            const capitalizedName = this.capitalizeFirst(safeVarName);

            handlers += `// Handle ${device.name} control\n`;
            handlers += `void handle${capitalizedName}Control() {\n`;

            switch (device.type) {
                case 'relay':
                case 'buzzer':
                case 'water-pump':
                    handlers += `  ${safeVarName}_state = !${safeVarName}_state;\n`;
                    handlers += `  digitalWrite(${safeVarName}_PIN, ${safeVarName}_state ? HIGH : LOW);\n`;
                    break;

                case 'pwm':
                case 'fan':
                    handlers += '  if (server.hasArg("value")) {\n';
                    handlers += '    int value = server.arg("value").toInt();\n';
                    handlers += '    if (value >= 0 && value <= 1023) {\n';
                    handlers += `      ${safeVarName}_value = value;\n`;
                    handlers += `      analogWrite(${safeVarName}_PIN, value);\n`;
                    handlers += '    }\n';
                    handlers += '  }\n';
                    break;

                case 'servo':
                    handlers += '  if (server.hasArg("value")) {\n';
                    handlers += '    int angle = server.arg("value").toInt();\n';
                    handlers += '    if (angle >= 0 && angle <= 180) {\n';
                    handlers += `      ${safeVarName}_angle = angle;\n`;
                    handlers += `      ${safeVarName}.write(angle);\n`;
                    handlers += '    }\n';
                    handlers += '  }\n';
                    break;

                case 'led-strip':
                    handlers += '  if (server.hasArg("brightness")) {\n';
                    handlers += '    int brightness = server.arg("brightness").toInt();\n';
                    handlers += '    if (brightness >= 0 && brightness <= 255) {\n';
                    handlers += `      ${safeVarName}_brightness = brightness;\n`;
                    handlers += `      ${safeVarName}.setBrightness(brightness);\n`;
                    handlers += `      ${safeVarName}.show();\n`;
                    handlers += '    }\n';
                    handlers += '  } else {\n';
                    handlers += `    ${safeVarName}_state = !${safeVarName}_state;\n`;
                    handlers += `    if (${safeVarName}_state) {\n`;
                    handlers += `      ${safeVarName}.setBrightness(${safeVarName}_brightness);\n`;
                    handlers += `      for (int i = 0; i < ${safeVarName}_NUM_LEDS; i++) {\n`;
                    handlers += `        ${safeVarName}.setPixelColor(i, ${safeVarName}_color);\n`;
                    handlers += '      }\n';
                    handlers += `      ${safeVarName}.show();\n`;
                    handlers += '    } else {\n';
                    handlers += `      ${safeVarName}.clear();\n`;
                    handlers += `      ${safeVarName}.show();\n`;
                    handlers += '    }\n';
                    handlers += '  }\n';
                    break;
            }

            handlers += '  server.sendHeader("Location", "/");\n';
            handlers += '  server.send(302, "text/plain", "");\n';
            handlers += '}\n\n';
        });

        return handlers;
    }

    // Helper methods

    // Generate a safe variable name from a user-friendly name
    safeVarName(name) {
        return name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    }

    // Capitalize the first letter of a string
    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
}