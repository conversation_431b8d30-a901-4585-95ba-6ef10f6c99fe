// Component Manager class
class ComponentManager {
    constructor(app) {
        this.app = app;
        this.components = [];
        this.nextId = 1;
        
        // Define available GPIO pins for ESP8266
        this.availablePins = [
            { value: 0, label: 'D0 (GPIO 0)' },
            { value: 1, label: 'D1 (GPIO 1)' },
            { value: 2, label: 'D2 (GPIO 2)' },
            { value: 3, label: 'D3 (GPIO 3)' },
            { value: 4, label: 'D4 (GPIO 4)' },
            { value: 5, label: 'D5 (GPIO 5)' },
            { value: 12, label: 'D6 (GPIO 12)' },
            { value: 13, label: 'D7 (GPIO 13)' },
            { value: 14, label: 'D8 (GPIO 14)' },
            { value: 15, label: 'D9 (GPIO 15)' },
            { value: 16, label: 'D10 (GPIO 16)' }
        ];
        
        // Component definitions
        this.componentDefinitions = {
            // Output devices
            'relay': {
                name: 'Relay',
                type: 'output',
                icon: 'toggle-on',
                defaults: {
                    initialState: 'LOW'
                },
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'initialState', label: 'Initial State', type: 'select', options: [
                        { value: 'LOW', label: 'OFF' },
                        { value: 'HIGH', label: 'ON' }
                    ], required: true }
                ]
            },
            'pwm': {
                name: 'PWM',
                type: 'output',
                icon: 'sliders-h',
                defaults: {
                    initialValue: 0
                },
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'initialValue', label: 'Initial Value (0-1023)', type: 'number', min: 0, max: 1023, required: true }
                ]
            },
            'fan': {
                name: 'Fan',
                type: 'output',
                icon: 'fan',
                defaults: {
                    initialValue: 0
                },
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'initialValue', label: 'Initial Speed (0-1023)', type: 'number', min: 0, max: 1023, required: true }
                ]
            },
            'buzzer': {
                name: 'Buzzer',
                type: 'output',
                icon: 'volume-up',
                defaults: {
                    initialState: 'LOW'
                },
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'initialState', label: 'Initial State', type: 'select', options: [
                        { value: 'LOW', label: 'OFF' },
                        { value: 'HIGH', label: 'ON' }
                    ], required: true }
                ]
            },
            'water-pump': {
                name: 'Water Pump',
                type: 'output',
                icon: 'tint',
                defaults: {
                    initialState: 'LOW'
                },
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'initialState', label: 'Initial State', type: 'select', options: [
                        { value: 'LOW', label: 'OFF' },
                        { value: 'HIGH', label: 'ON' }
                    ], required: true }
                ]
            },
            'servo': {
                name: 'Servo Motor',
                type: 'output',
                icon: 'cog',
                defaults: {
                    initialAngle: 90
                },
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'initialAngle', label: 'Initial Angle (0-180)', type: 'number', min: 0, max: 180, required: true }
                ]
            },
            'led-strip': {
                name: 'LED Strip',
                type: 'output',
                icon: 'lightbulb',
                defaults: {
                    numLeds: 8,
                    dataPin: 4
                },
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'dataPin', label: 'Data Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'numLeds', label: 'Number of LEDs', type: 'number', min: 1, max: 300, required: true }
                ]
            },
            
            // Sensors
            'dht11': {
                name: 'DHT11',
                type: 'sensor',
                icon: 'thermometer-half',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true }
                ],
                readings: ['temperature', 'humidity']
            },
            'rain': {
                name: 'Rain Sensor',
                type: 'sensor',
                icon: 'cloud-rain',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'Analog Pin (A0)', type: 'select', options: [
                        { value: 'A0', label: 'A0' }
                    ], required: true }
                ],
                readings: ['rainLevel']
            },
            'ldr': {
                name: 'LDR',
                type: 'sensor',
                icon: 'sun',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'Analog Pin (A0)', type: 'select', options: [
                        { value: 'A0', label: 'A0' }
                    ], required: true }
                ],
                readings: ['lightLevel']
            },
            'pir': {
                name: 'PIR',
                type: 'sensor',
                icon: 'walking',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true }
                ],
                readings: ['motion']
            },
            'ultrasonic': {
                name: 'Ultrasonic',
                type: 'sensor',
                icon: 'wave-square',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'trigPin', label: 'Trigger Pin', type: 'select', options: this.availablePins, required: true },
                    { name: 'echoPin', label: 'Echo Pin', type: 'select', options: this.availablePins, required: true }
                ],
                readings: ['distance']
            },
            'soil': {
                name: 'Soil Moisture',
                type: 'sensor',
                icon: 'seedling',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'Analog Pin (A0)', type: 'select', options: [
                        { value: 'A0', label: 'A0' }
                    ], required: true }
                ],
                readings: ['moistureLevel']
            },
            'flame': {
                name: 'Flame Sensor',
                type: 'sensor',
                icon: 'fire',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'Analog Pin (A0)', type: 'select', options: [
                        { value: 'A0', label: 'A0' }
                    ], required: true }
                ],
                readings: ['flameLevel']
            },
            'gas': {
                name: 'Gas Sensor (MQ-2)',
                type: 'sensor',
                icon: 'smog',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'Analog Pin (A0)', type: 'select', options: [
                        { value: 'A0', label: 'A0' }
                    ], required: true }
                ],
                readings: ['gasLevel']
            },
            'ir': {
                name: 'IR Receiver',
                type: 'sensor',
                icon: 'rss',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'GPIO Pin', type: 'select', options: this.availablePins, required: true }
                ],
                readings: ['irCode']
            },
            'sound': {
                name: 'Sound Sensor',
                type: 'sensor',
                icon: 'microphone',
                defaults: {},
                properties: [
                    { name: 'name', label: 'Name', type: 'text', required: true },
                    { name: 'pin', label: 'Analog Pin (A0)', type: 'select', options: [
                        { value: 'A0', label: 'A0' }
                    ], required: true }
                ],
                readings: ['soundLevel']
            }
        };
        
        this.initEventListeners();
    }
    
    initEventListeners() {
        // Add component button
        const addComponentBtn = document.getElementById('add-component-btn');
        addComponentBtn.addEventListener('click', () => {
            this.openComponentModal();
        });
        
        // Component type buttons
        const componentTypeBtns = document.querySelectorAll('.component-type-btn');
        componentTypeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const type = btn.dataset.type;
                
                // Toggle active class
                componentTypeBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Show/hide component groups
                document.querySelectorAll('.component-group').forEach(group => {
                    group.classList.remove('active');
                });
                document.getElementById(`${type}-components`).classList.add('active');
                
                // Hide component form
                document.getElementById('component-form').classList.add('hidden');
            });
        });
        
        // Component items
        const componentItems = document.querySelectorAll('.component-item');
        componentItems.forEach(item => {
            item.addEventListener('click', () => {
                const componentType = item.dataset.component;
                this.showComponentForm(componentType);
            });
        });
        
        // Close modal
        const closeButtons = document.querySelectorAll('.modal .close');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const modal = btn.closest('.modal');
                this.closeModal(modal);
            });
        });
        
        // Click outside modal to close
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal);
                }
            });
        });
    }
    
    openComponentModal() {
        const modal = document.getElementById('component-modal');
        modal.style.display = 'flex';
        
        // Reset modal state
        const componentTypeBtns = document.querySelectorAll('.component-type-btn');
        componentTypeBtns[0].click(); // Click first button (output devices)
    }
    
    closeModal(modal) {
        modal.style.display = 'none';
    }
    
    showComponentForm(componentType) {
        const componentDef = this.componentDefinitions[componentType];
        const componentForm = document.getElementById('component-form');
        
        // Set form heading and clear previous content
        componentForm.innerHTML = `
            <h3>Add ${componentDef.name}</h3>
            <form id="new-component-form">
                ${this.generateFormFields(componentDef)}
                <div class="form-actions">
                    <button type="button" id="cancel-component-btn" class="secondary-btn">Cancel</button>
                    <button type="submit" class="primary-btn">Add Component</button>
                </div>
            </form>
        `;
        
        // Show the form
        componentForm.classList.remove('hidden');
        
        // Add event listeners
        document.getElementById('cancel-component-btn').addEventListener('click', () => {
            componentForm.classList.add('hidden');
        });
        
        document.getElementById('new-component-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(e.target);
            const componentData = {
                id: this.nextId++,
                type: componentType,
                ...Object.fromEntries(formData.entries())
            };
            
            // Add component
            this.addComponent(componentData);
            
            // Close modal
            this.closeModal(document.getElementById('component-modal'));
        });
    }
    
    generateFormFields(componentDef) {
        let fieldsHtml = '';
        
        for (const prop of componentDef.properties) {
            const defaultValue = componentDef.defaults[prop.name] || '';
            
            fieldsHtml += `
                <div class="form-group">
                    <label for="${prop.name}">${prop.label}${prop.required ? ' *' : ''}</label>
            `;
            
            if (prop.type === 'text') {
                fieldsHtml += `
                    <input type="text" id="${prop.name}" name="${prop.name}" class="form-control" 
                        value="${defaultValue}" ${prop.required ? 'required' : ''}>
                `;
            } else if (prop.type === 'number') {
                fieldsHtml += `
                    <input type="number" id="${prop.name}" name="${prop.name}" class="form-control" 
                        value="${defaultValue}" min="${prop.min || 0}" max="${prop.max || ''}" 
                        ${prop.required ? 'required' : ''}>
                `;
            } else if (prop.type === 'select') {
                fieldsHtml += `
                    <select id="${prop.name}" name="${prop.name}" class="form-control" ${prop.required ? 'required' : ''}>
                        <option value="" disabled ${!defaultValue ? 'selected' : ''}>Select ${prop.label}</option>
                        ${prop.options.map(opt => `
                            <option value="${opt.value}" ${defaultValue === opt.value ? 'selected' : ''}>
                                ${opt.label}
                            </option>
                        `).join('')}
                    </select>
                `;
            }
            
            fieldsHtml += `
                </div>
            `;
        }
        
        return fieldsHtml;
    }
    
    addComponent(componentData) {
        // Add to components array
        this.components.push(componentData);
        
        // Render the component in the UI
        this.renderComponent(componentData);
        
        console.log('Component added:', componentData);
    }
    
    renderComponent(component) {
        const componentsList = document.getElementById('components-list');
        const componentDef = this.componentDefinitions[component.type];
        
        // Create component card
        const componentCard = document.createElement('div');
        componentCard.className = 'component-card';
        componentCard.dataset.id = component.id;
        
        // Component header
        const header = document.createElement('div');
        header.className = 'component-header';
        
        // Component name
        const nameDiv = document.createElement('div');
        nameDiv.className = 'component-name';
        nameDiv.innerHTML = `<i class="fas fa-${componentDef.icon}"></i> ${component.name}`;
        
        // Component actions
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'component-actions';
        actionsDiv.innerHTML = `
            <button class="edit-component-btn" title="Edit"><i class="fas fa-edit"></i></button>
            <button class="delete-component-btn" title="Delete"><i class="fas fa-trash"></i></button>
        `;
        
        header.appendChild(nameDiv);
        header.appendChild(actionsDiv);
        componentCard.appendChild(header);
        
        // Component details
        const detailsDiv = document.createElement('div');
        detailsDiv.className = 'component-details';
        
        // Add component type
        detailsDiv.innerHTML = `
            <div class="component-detail">
                <span>Type:</span>
                <span>${componentDef.name}</span>
            </div>
        `;
        
        // Add pin information
        if (component.pin) {
            const pinLabel = this.availablePins.find(p => p.value == component.pin)?.label || component.pin;
            detailsDiv.innerHTML += `
                <div class="component-detail">
                    <span>GPIO Pin:</span>
                    <span>${pinLabel}</span>
                </div>
            `;
        }
        
        // Add special pin information for ultrasonic
        if (component.type === 'ultrasonic') {
            const trigPinLabel = this.availablePins.find(p => p.value == component.trigPin)?.label || component.trigPin;
            const echoPinLabel = this.availablePins.find(p => p.value == component.echoPin)?.label || component.echoPin;
            
            detailsDiv.innerHTML += `
                <div class="component-detail">
                    <span>Trigger Pin:</span>
                    <span>${trigPinLabel}</span>
                </div>
                <div class="component-detail">
                    <span>Echo Pin:</span>
                    <span>${echoPinLabel}</span>
                </div>
            `;
        }
        
        // Add initial state/value for output devices
        if (componentDef.type === 'output') {
            if (component.initialState) {
                detailsDiv.innerHTML += `
                    <div class="component-detail">
                        <span>Initial State:</span>
                        <span>${component.initialState === 'HIGH' ? 'ON' : 'OFF'}</span>
                    </div>
                `;
            } else if (component.initialValue) {
                detailsDiv.innerHTML += `
                    <div class="component-detail">
                        <span>Initial Value:</span>
                        <span>${component.initialValue}</span>
                    </div>
                `;
            } else if (component.initialAngle) {
                detailsDiv.innerHTML += `
                    <div class="component-detail">
                        <span>Initial Angle:</span>
                        <span>${component.initialAngle}°</span>
                    </div>
                `;
            }
        }
        
        componentCard.appendChild(detailsDiv);
        componentsList.appendChild(componentCard);
        
        // Add event listeners
        componentCard.querySelector('.edit-component-btn').addEventListener('click', () => {
            this.editComponent(component.id);
        });
        
        componentCard.querySelector('.delete-component-btn').addEventListener('click', () => {
            this.deleteComponent(component.id);
        });
    }
    
    editComponent(id) {
        const component = this.components.find(c => c.id === id);
        if (!component) return;
        
        // TODO: Implement edit functionality
        console.log('Edit component:', component);
    }
    
    deleteComponent(id) {
        // Find the component
        const index = this.components.findIndex(c => c.id === id);
        if (index === -1) return;
        
        // Remove from array
        this.components.splice(index, 1);
        
        // Remove from UI
        const componentCard = document.querySelector(`.component-card[data-id="${id}"]`);
        if (componentCard) {
            componentCard.remove();
        }
        
        console.log('Component deleted:', id);
    }
    
    getComponents() {
        return this.components;
    }
    
    getOutputComponents() {
        return this.components.filter(c => this.componentDefinitions[c.type].type === 'output');
    }
    
    getSensorComponents() {
        return this.components.filter(c => this.componentDefinitions[c.type].type === 'sensor');
    }
    
    getComponentById(id) {
        return this.components.find(c => c.id === id);
    }
    
    getComponentDefinition(type) {
        return this.componentDefinitions[type];
    }
    
    // Method to check if a pin is already used
    isPinUsed(pin, excludeId = null) {
        return this.components.some(c => {
            if (c.id === excludeId) return false;
            
            if (c.type === 'ultrasonic') {
                return c.trigPin == pin || c.echoPin == pin;
            }
            
            return c.pin == pin;
        });
    }
} 