// Automation Manager class
class AutomationManager {
    constructor(app) {
        this.app = app;
        this.rules = [];
        this.nextId = 1;
        
        this.initEventListeners();
    }
    
    initEventListeners() {
        // Add rule button
        const addRuleBtn = document.getElementById('add-rule-btn');
        addRuleBtn.addEventListener('click', () => {
            this.openRuleModal();
        });
        
        // Save rule button
        document.getElementById('save-rule-btn').addEventListener('click', () => {
            this.saveRule();
        });
        
        // Cancel rule button
        document.getElementById('cancel-rule-btn').addEventListener('click', () => {
            this.closeRuleModal();
        });
        
        // Add action button
        document.getElementById('add-action-btn').addEventListener('click', () => {
            this.addActionRow();
        });
        
        // Duration checkbox
        document.querySelector('.duration-check').addEventListener('change', (e) => {
            const durationInput = e.target.closest('.duration-container').querySelector('.duration-value');
            const durationUnit = e.target.closest('.duration-container').querySelector('.duration-unit');
            
            durationInput.disabled = !e.target.checked;
            durationUnit.disabled = !e.target.checked;
        });
        
        // Action type change
        document.querySelector('.action-type').addEventListener('change', (e) => {
            const valueInput = e.target.closest('.action-row').querySelector('.action-value');
            
            if (e.target.value === 'setValue') {
                valueInput.classList.remove('hidden');
            } else {
                valueInput.classList.add('hidden');
            }
        });
    }
    
    openRuleModal() {
        const modal = document.getElementById('rule-modal');
        modal.style.display = 'flex';
        
        // Reset form
        document.querySelector('.rule-form').reset();
        
        // Clear existing action rows except the first one
        const actionList = document.getElementById('action-list');
        const actionRows = actionList.querySelectorAll('.action-row');
        
        if (actionRows.length > 1) {
            for (let i = 1; i < actionRows.length; i++) {
                actionRows[i].remove();
            }
        }
        
        // Reset first action row
        const firstActionRow = actionList.querySelector('.action-row');
        firstActionRow.querySelector('.action-type').value = 'on';
        firstActionRow.querySelector('.action-value').classList.add('hidden');
        firstActionRow.querySelector('.duration-check').checked = false;
        firstActionRow.querySelector('.duration-value').disabled = true;
        firstActionRow.querySelector('.duration-unit').disabled = true;
        
        // Populate select dropdowns with available components
        this.populateComponentSelects();
    }
    
    closeRuleModal() {
        const modal = document.getElementById('rule-modal');
        modal.style.display = 'none';
    }
    
    populateComponentSelects() {
        // Get available sensors
        const sensors = this.app.componentManager.getSensorComponents();
        const conditionSensorSelect = document.getElementById('condition-sensor');
        
        // Clear existing options
        conditionSensorSelect.innerHTML = '<option value="" disabled selected>Select Sensor</option>';
        
        // Add sensor options
        sensors.forEach(sensor => {
            const sensorDef = this.app.componentManager.getComponentDefinition(sensor.type);
            const readings = sensorDef.readings || [];
            
            readings.forEach(reading => {
                const option = document.createElement('option');
                option.value = `${sensor.id}:${reading}`;
                option.textContent = `${sensor.name} (${this.formatReadingName(reading)})`;
                conditionSensorSelect.appendChild(option);
            });
        });
        
        // Get available output devices
        const outputDevices = this.app.componentManager.getOutputComponents();
        const actionDeviceSelects = document.querySelectorAll('.action-device');
        
        // Update all action device selects
        actionDeviceSelects.forEach(select => {
            // Clear existing options
            select.innerHTML = '<option value="" disabled selected>Select Device</option>';
            
            // Add output device options
            outputDevices.forEach(device => {
                const option = document.createElement('option');
                option.value = device.id;
                option.textContent = device.name;
                select.appendChild(option);
            });
        });
    }
    
    formatReadingName(reading) {
        const formattedNames = {
            'temperature': 'Temperature',
            'humidity': 'Humidity',
            'rainLevel': 'Rain Level',
            'lightLevel': 'Light Level',
            'motion': 'Motion',
            'distance': 'Distance',
            'moistureLevel': 'Moisture Level',
            'flameLevel': 'Flame Level',
            'gasLevel': 'Gas Level',
            'irCode': 'IR Code',
            'soundLevel': 'Sound Level'
        };
        
        return formattedNames[reading] || reading;
    }
    
    addActionRow() {
        const actionList = document.getElementById('action-list');
        const actionRows = actionList.querySelectorAll('.action-row');
        
        // Enable remove button on existing rows
        actionRows.forEach(row => {
            row.querySelector('.remove-action-btn').disabled = false;
        });
        
        // Create new action row
        const newRow = document.createElement('div');
        newRow.className = 'action-row';
        
        newRow.innerHTML = `
            <select class="action-device form-control">
                <option value="" disabled selected>Select Device</option>
            </select>
            <select class="action-type form-control">
                <option value="on">Turn ON</option>
                <option value="off">Turn OFF</option>
                <option value="setValue">Set Value</option>
            </select>
            <input type="number" class="action-value form-control hidden" placeholder="Value">
            <div class="duration-container">
                <label>
                    <input type="checkbox" class="duration-check">
                    For
                </label>
                <input type="number" class="duration-value form-control" placeholder="Duration" disabled>
                <select class="duration-unit form-control" disabled>
                    <option value="seconds">Seconds</option>
                    <option value="minutes">Minutes</option>
                    <option value="hours">Hours</option>
                </select>
            </div>
            <button class="remove-action-btn"><i class="fas fa-trash"></i></button>
        `;
        
        actionList.appendChild(newRow);
        
        // Populate device select with available output devices
        const outputDevices = this.app.componentManager.getOutputComponents();
        const deviceSelect = newRow.querySelector('.action-device');
        
        outputDevices.forEach(device => {
            const option = document.createElement('option');
            option.value = device.id;
            option.textContent = device.name;
            deviceSelect.appendChild(option);
        });
        
        // Add event listeners to new elements
        newRow.querySelector('.action-type').addEventListener('change', (e) => {
            const valueInput = e.target.closest('.action-row').querySelector('.action-value');
            
            if (e.target.value === 'setValue') {
                valueInput.classList.remove('hidden');
            } else {
                valueInput.classList.add('hidden');
            }
        });
        
        newRow.querySelector('.duration-check').addEventListener('change', (e) => {
            const durationInput = e.target.closest('.duration-container').querySelector('.duration-value');
            const durationUnit = e.target.closest('.duration-container').querySelector('.duration-unit');
            
            durationInput.disabled = !e.target.checked;
            durationUnit.disabled = !e.target.checked;
        });
        
        newRow.querySelector('.remove-action-btn').addEventListener('click', (e) => {
            const actionRow = e.target.closest('.action-row');
            actionRow.remove();
            
            // If only one action row remains, disable its remove button
            const remainingRows = actionList.querySelectorAll('.action-row');
            if (remainingRows.length === 1) {
                remainingRows[0].querySelector('.remove-action-btn').disabled = true;
            }
        });
    }
    
    saveRule() {
        // Get condition data
        const conditionSensor = document.getElementById('condition-sensor').value;
        if (!conditionSensor) {
            this.app.showNotification('Please select a sensor', 'error');
            return;
        }
        
        const conditionOperator = document.getElementById('condition-operator').value;
        const conditionValue = document.getElementById('condition-value').value;
        
        if (!conditionValue && conditionValue !== '0') {
            this.app.showNotification('Please enter a condition value', 'error');
            return;
        }
        
        // Get action data
        const actionRows = document.querySelectorAll('.action-row');
        const actions = [];
        
        for (const row of actionRows) {
            const deviceId = row.querySelector('.action-device').value;
            if (!deviceId) {
                this.app.showNotification('Please select a device for all actions', 'error');
                return;
            }
            
            const actionType = row.querySelector('.action-type').value;
            let value = null;
            
            if (actionType === 'setValue') {
                value = row.querySelector('.action-value').value;
                if (!value && value !== '0') {
                    this.app.showNotification('Please enter a value for all Set Value actions', 'error');
                    return;
                }
            }
            
            const hasDuration = row.querySelector('.duration-check').checked;
            let duration = null;
            
            if (hasDuration) {
                const durationValue = row.querySelector('.duration-value').value;
                if (!durationValue) {
                    this.app.showNotification('Please enter a duration value', 'error');
                    return;
                }
                
                const durationUnit = row.querySelector('.duration-unit').value;
                duration = {
                    value: parseInt(durationValue),
                    unit: durationUnit
                };
            }
            
            actions.push({
                deviceId: parseInt(deviceId),
                type: actionType,
                value: value ? parseInt(value) : null,
                duration
            });
        }
        
        if (actions.length === 0) {
            this.app.showNotification('Please add at least one action', 'error');
            return;
        }
        
        // Create rule object
        const [sensorId, reading] = conditionSensor.split(':');
        
        const rule = {
            id: this.nextId++,
            condition: {
                sensorId: parseInt(sensorId),
                reading,
                operator: conditionOperator,
                value: parseInt(conditionValue)
            },
            actions
        };
        
        // Add rule
        this.addRule(rule);
        
        // Close modal
        this.closeRuleModal();
    }
    
    addRule(rule) {
        // Add to rules array
        this.rules.push(rule);
        
        // Render the rule in the UI
        this.renderRule(rule);
        
        console.log('Rule added:', rule);
    }
    
    renderRule(rule) {
        const rulesList = document.getElementById('rules-list');
        
        // Get sensor and device info
        const sensor = this.app.componentManager.getComponentById(rule.condition.sensorId);
        
        // Create rule card
        const ruleCard = document.createElement('div');
        ruleCard.className = 'rule-card';
        ruleCard.dataset.id = rule.id;
        
        // Rule header
        const header = document.createElement('div');
        header.className = 'rule-header';
        
        // Rule title
        const titleDiv = document.createElement('div');
        titleDiv.className = 'rule-title';
        titleDiv.textContent = `Rule #${rule.id}`;
        
        // Rule actions
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'rule-card-actions';
        actionsDiv.innerHTML = `
            <button class="edit-rule-btn secondary-btn">Edit</button>
            <button class="delete-rule-btn secondary-btn">Delete</button>
        `;
        
        header.appendChild(titleDiv);
        header.appendChild(actionsDiv);
        ruleCard.appendChild(header);
        
        // Rule condition
        const conditionDiv = document.createElement('div');
        conditionDiv.className = 'rule-condition';
        
        const conditionHeading = document.createElement('h3');
        conditionHeading.textContent = 'IF';
        
        const conditionText = document.createElement('div');
        conditionText.className = 'condition-text';
        
        const operatorSymbol = rule.condition.operator === 'gt' ? '>' : rule.condition.operator === 'lt' ? '<' : '==';
        const formattedReading = this.formatReadingName(rule.condition.reading);
        
        conditionText.textContent = `${sensor.name} (${formattedReading}) ${operatorSymbol} ${rule.condition.value}`;
        
        conditionDiv.appendChild(conditionHeading);
        conditionDiv.appendChild(conditionText);
        ruleCard.appendChild(conditionDiv);
        
        // Rule actions
        const actionsSection = document.createElement('div');
        actionsSection.className = 'rule-actions';
        
        const actionsHeading = document.createElement('h3');
        actionsHeading.textContent = 'THEN';
        
        const actionsList = document.createElement('div');
        actionsList.className = 'action-list';
        
        // Add each action
        rule.actions.forEach((action, index) => {
            const device = this.app.componentManager.getComponentById(action.deviceId);
            
            const actionText = document.createElement('div');
            actionText.className = 'action-text';
            
            let actionString = '';
            
            if (action.type === 'on') {
                actionString = `Turn ON ${device.name}`;
            } else if (action.type === 'off') {
                actionString = `Turn OFF ${device.name}`;
            } else if (action.type === 'setValue') {
                actionString = `Set ${device.name} to ${action.value}`;
            }
            
            if (action.duration) {
                actionString += ` for ${action.duration.value} ${action.duration.unit}`;
            }
            
            actionText.textContent = actionString;
            actionsList.appendChild(actionText);
            
            // Add spacing between actions
            if (index < rule.actions.length - 1) {
                const spacer = document.createElement('div');
                spacer.className = 'action-spacer';
                spacer.textContent = 'AND';
                actionsList.appendChild(spacer);
            }
        });
        
        actionsSection.appendChild(actionsHeading);
        actionsSection.appendChild(actionsList);
        ruleCard.appendChild(actionsSection);
        
        // Add to list
        rulesList.appendChild(ruleCard);
        
        // Add event listeners
        ruleCard.querySelector('.edit-rule-btn').addEventListener('click', () => {
            this.editRule(rule.id);
        });
        
        ruleCard.querySelector('.delete-rule-btn').addEventListener('click', () => {
            this.deleteRule(rule.id);
        });
    }
    
    editRule(id) {
        const rule = this.rules.find(r => r.id === id);
        if (!rule) return;
        
        // TODO: Implement edit functionality
        console.log('Edit rule:', rule);
    }
    
    deleteRule(id) {
        // Find the rule
        const index = this.rules.findIndex(r => r.id === id);
        if (index === -1) return;
        
        // Remove from array
        this.rules.splice(index, 1);
        
        // Remove from UI
        const ruleCard = document.querySelector(`.rule-card[data-id="${id}"]`);
        if (ruleCard) {
            ruleCard.remove();
        }
        
        console.log('Rule deleted:', id);
    }
    
    getRules() {
        return this.rules;
    }
} 