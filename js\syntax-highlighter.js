// Simple Arduino C++ Syntax Highlighter
class SyntaxHighlighter {
    constructor() {
        // Define syntax patterns
        this.patterns = [
            // Comments
            { pattern: /(\/\/.*$)/gm, className: 'comment' },
            { pattern: /(\/\*[\s\S]*?\*\/)/g, className: 'comment' },
            
            // Preprocessor directives
            { pattern: /(#\w+)/g, className: 'preprocessor' },
            
            // Keywords
            { pattern: /\b(void|int|float|double|char|bool|String|const|unsigned|long|short|byte|word|boolean|true|false|HIGH|LOW|INPUT|OUTPUT|INPUT_PULLUP)\b/g, className: 'keyword' },
            
            // Arduino functions
            { pattern: /\b(setup|loop|pinMode|digitalWrite|digitalRead|analogWrite|analogRead|delay|delayMicroseconds|millis|micros|Serial|WiFi|server|client)\b/g, className: 'function' },
            
            // Types and classes
            { pattern: /\b(DHT|Servo|ESP8266WebServer|WebServer|Adafruit_NeoPixel|IRrecv|decode_results)\b/g, className: 'type' },
            
            // String literals
            { pattern: /"([^"\\]|\\.)*"/g, className: 'string' },
            { pattern: /'([^'\\]|\\.)*'/g, className: 'string' },
            
            // Numbers
            { pattern: /\b\d+\.?\d*\b/g, className: 'number' },
            
            // Function calls (word followed by parentheses)
            { pattern: /\b([a-zA-Z_]\w*)\s*(?=\()/g, className: 'function' }
        ];
    }
    
    highlight(code) {
        if (!code) return '';
        
        let highlightedCode = code;
        
        // Apply each pattern
        this.patterns.forEach(({ pattern, className }) => {
            highlightedCode = highlightedCode.replace(pattern, (match) => {
                return `<span class="${className}">${this.escapeHtml(match)}</span>`;
            });
        });
        
        return highlightedCode;
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Apply syntax highlighting to a code element
    highlightElement(element) {
        if (!element) return;
        
        const code = element.textContent || element.innerText;
        element.innerHTML = this.highlight(code);
    }
    
    // Auto-highlight all code containers
    highlightAll() {
        const codeContainers = document.querySelectorAll('.code-container, pre code, .arduino-code');
        codeContainers.forEach(container => {
            this.highlightElement(container);
        });
    }
}

// Create global instance
window.syntaxHighlighter = new SyntaxHighlighter();
