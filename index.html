<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP Automation Designer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="app-container">
        <header>
            <div class="header-content">
                <div class="header-title">
                    <h1>ESP Automation Designer</h1>
                    <p class="header-subtitle">by SKR Electronics Lab</p>
                </div>
                <div class="header-controls">
                    <div class="board-selector">
                        <label for="board-select">Board:</label>
                        <select id="board-select" class="form-control">
                            <option value="ESP8266">ESP8266</option>
                            <option value="ESP32">ESP32</option>
                        </select>
                    </div>
                    <div class="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </div>
                </div>
            </div>
        </header>

        <div class="tabs">
            <button class="tab-btn active" data-tab="designer">Designer</button>
            <button class="tab-btn" data-tab="automation">Automation</button>
            <button class="tab-btn" data-tab="wifi">WiFi Config</button>
            <button class="tab-btn" data-tab="code">Code</button>
        </div>

        <main>
            <!-- Designer Tab -->
            <section id="designer" class="tab-content active">
                <div class="components-panel">
                    <h2>Components</h2>
                    <div class="component-adder">
                        <button id="add-component-btn" class="add-btn"><i class="fas fa-plus"></i> Add Component</button>
                    </div>
                    <div id="components-list" class="components-list">
                        <!-- Components will be added here dynamically -->
                    </div>
                </div>
            </section>

            <!-- Automation Tab -->
            <section id="automation" class="tab-content">
                <div class="automation-panel">
                    <h2>Automation Rules</h2>
                    <button id="add-rule-btn" class="add-btn"><i class="fas fa-plus"></i> Add Rule</button>
                    <div id="rules-list" class="rules-list">
                        <!-- Rules will be added here dynamically -->
                    </div>
                </div>
            </section>

            <!-- WiFi Configuration Tab -->
            <section id="wifi" class="tab-content">
                <div class="wifi-panel">
                    <h2>WiFi Configuration</h2>
                    <div class="wifi-config">
                        <div class="config-section">
                            <h3>Access Point Mode (Default)</h3>
                            <div class="form-group">
                                <label for="ap-ssid">Access Point Name (SSID):</label>
                                <input type="text" id="ap-ssid" class="form-control" value="ESP_AutomationHub" placeholder="Enter AP name">
                            </div>
                            <div class="form-group">
                                <label for="ap-password">Access Point Password:</label>
                                <input type="password" id="ap-password" class="form-control" value="12345678" placeholder="Enter AP password (min 8 chars)">
                            </div>
                        </div>

                        <div class="config-section">
                            <h3>Station Mode (Connect to existing WiFi)</h3>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enable-station-mode"> Enable Station Mode
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="wifi-ssid">WiFi Network Name (SSID):</label>
                                <input type="text" id="wifi-ssid" class="form-control" placeholder="Enter your WiFi network name" disabled>
                            </div>
                            <div class="form-group">
                                <label for="wifi-password">WiFi Password:</label>
                                <input type="password" id="wifi-password" class="form-control" placeholder="Enter your WiFi password" disabled>
                            </div>
                        </div>

                        <div class="config-section">
                            <h3>Web Server Settings</h3>
                            <div class="form-group">
                                <label for="server-port">Server Port:</label>
                                <input type="number" id="server-port" class="form-control" value="80" min="1" max="65535">
                            </div>
                        </div>

                        <div class="wifi-actions">
                            <button id="reset-wifi-btn" class="secondary-btn">Reset to Defaults</button>
                            <button id="test-wifi-btn" class="primary-btn">Test Configuration</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Code Tab -->
            <section id="code" class="tab-content">
                <div class="code-panel">
                    <h2>Generated Code</h2>
                    <div class="code-actions">
                        <button id="generate-code-btn" class="primary-btn">Generate Arduino Code</button>
                        <button id="copy-arduino-btn" class="secondary-btn">Copy Arduino Code</button>
                        <button id="download-btn" class="secondary-btn">Download Arduino Code</button>
                    </div>
                    <div class="code-preview">
                        <div class="code-containers">
                            <pre id="arduino-code" class="code-container active"></pre>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <div>
                <button id="save-project-btn" class="secondary-btn"><i class="fas fa-save"></i> Save Project</button>
                <button id="load-project-btn" class="secondary-btn"><i class="fas fa-folder-open"></i> Load Project</button>
            </div>
            <p>ESP8266 Home Automation Designer</p>
        </footer>
    </div>

    <!-- Component Modal -->
    <div id="component-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Add New Component</h2>
            <div class="component-types">
                <button class="component-type-btn active" data-type="output">Output Devices</button>
                <button class="component-type-btn" data-type="sensor">Sensors</button>
            </div>
            <div class="component-selection">
                <div id="output-components" class="component-group active">
                    <div class="component-item" data-component="relay">
                        <i class="fas fa-toggle-on"></i>
                        <span>Relay</span>
                    </div>
                    <div class="component-item" data-component="pwm">
                        <i class="fas fa-sliders-h"></i>
                        <span>PWM</span>
                    </div>
                    <div class="component-item" data-component="fan">
                        <i class="fas fa-fan"></i>
                        <span>Fan</span>
                    </div>
                    <div class="component-item" data-component="buzzer">
                        <i class="fas fa-volume-up"></i>
                        <span>Buzzer</span>
                    </div>
                    <div class="component-item" data-component="water-pump">
                        <i class="fas fa-tint"></i>
                        <span>Water Pump</span>
                    </div>
                    <div class="component-item" data-component="servo">
                        <i class="fas fa-cog"></i>
                        <span>Servo Motor</span>
                    </div>
                    <div class="component-item" data-component="led-strip">
                        <i class="fas fa-lightbulb"></i>
                        <span>LED Strip</span>
                    </div>
                </div>
                <div id="sensor-components" class="component-group">
                    <div class="component-item" data-component="dht11">
                        <i class="fas fa-thermometer-half"></i>
                        <span>DHT11</span>
                    </div>
                    <div class="component-item" data-component="rain">
                        <i class="fas fa-cloud-rain"></i>
                        <span>Rain Sensor</span>
                    </div>
                    <div class="component-item" data-component="ldr">
                        <i class="fas fa-sun"></i>
                        <span>LDR</span>
                    </div>
                    <div class="component-item" data-component="pir">
                        <i class="fas fa-walking"></i>
                        <span>PIR</span>
                    </div>
                    <div class="component-item" data-component="ultrasonic">
                        <i class="fas fa-wave-square"></i>
                        <span>Ultrasonic</span>
                    </div>
                    <div class="component-item" data-component="soil">
                        <i class="fas fa-seedling"></i>
                        <span>Soil Moisture</span>
                    </div>
                    <div class="component-item" data-component="flame">
                        <i class="fas fa-fire"></i>
                        <span>Flame Sensor</span>
                    </div>
                    <div class="component-item" data-component="gas">
                        <i class="fas fa-smog"></i>
                        <span>Gas Sensor (MQ-2)</span>
                    </div>
                    <div class="component-item" data-component="ir">
                        <i class="fas fa-rss"></i>
                        <span>IR Receiver</span>
                    </div>
                    <div class="component-item" data-component="sound">
                        <i class="fas fa-microphone"></i>
                        <span>Sound Sensor</span>
                    </div>
                </div>
            </div>
            <div id="component-form" class="component-form hidden">
                <!-- Form will be dynamically generated based on component selection -->
            </div>
        </div>
    </div>

    <!-- Automation Rule Modal -->
    <div id="rule-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Add Automation Rule</h2>
            <div class="rule-form">
                <div class="rule-condition">
                    <h3>IF</h3>
                    <div class="condition-row">
                        <select id="condition-sensor" class="form-control">
                            <!-- Will be populated with available sensors -->
                        </select>
                        <select id="condition-operator" class="form-control">
                            <option value="gt">></option>
                            <option value="lt"><</option>
                            <option value="eq">==</option>
                        </select>
                        <input type="number" id="condition-value" class="form-control" placeholder="Value">
                    </div>
                </div>
                <div class="rule-actions">
                    <h3>THEN</h3>
                    <div id="action-list">
                        <div class="action-row">
                            <select class="action-device form-control">
                                <!-- Will be populated with available output devices -->
                            </select>
                            <select class="action-type form-control">
                                <option value="on">Turn ON</option>
                                <option value="off">Turn OFF</option>
                                <option value="setValue">Set Value</option>
                            </select>
                            <input type="number" class="action-value form-control hidden" placeholder="Value">
                            <div class="duration-container">
                                <label>
                                    <input type="checkbox" class="duration-check">
                                    For
                                </label>
                                <input type="number" class="duration-value form-control" placeholder="Duration" disabled>
                                <select class="duration-unit form-control" disabled>
                                    <option value="seconds">Seconds</option>
                                    <option value="minutes">Minutes</option>
                                    <option value="hours">Hours</option>
                                </select>
                            </div>
                            <button class="remove-action-btn" disabled><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    <button id="add-action-btn" class="add-btn small"><i class="fas fa-plus"></i> Add Action</button>
                </div>
                <div class="rule-form-actions">
                    <button id="save-rule-btn" class="primary-btn">Save Rule</button>
                    <button id="cancel-rule-btn" class="secondary-btn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load all scripts directly -->
    <script src="js/ui.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/automation.js"></script>
    <script src="js/wifi-config.js"></script>
    <script src="js/syntax-highlighter.js"></script>
    <script src="js/code-generator.js"></script>
    <script src="js/app.js"></script>
</body>
</html> 