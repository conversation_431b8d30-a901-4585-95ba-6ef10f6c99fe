// WiFi Configuration Manager class
class WiFiConfigManager {
    constructor(app) {
        this.app = app;
        this.config = {
            apMode: {
                ssid: 'ESP_AutomationHub',
                password: '12345678'
            },
            stationMode: {
                enabled: false,
                ssid: '',
                password: ''
            },
            serverPort: 80
        };
        
        this.initEventListeners();
        this.loadConfig();
    }
    
    initEventListeners() {
        // Station mode toggle
        const enableStationMode = document.getElementById('enable-station-mode');
        enableStationMode.addEventListener('change', (e) => {
            const wifiInputs = document.querySelectorAll('#wifi-ssid, #wifi-password');
            wifiInputs.forEach(input => {
                input.disabled = !e.target.checked;
            });
            this.config.stationMode.enabled = e.target.checked;
        });
        
        // Input change listeners
        document.getElementById('ap-ssid').addEventListener('input', (e) => {
            this.config.apMode.ssid = e.target.value;
        });
        
        document.getElementById('ap-password').addEventListener('input', (e) => {
            this.config.apMode.password = e.target.value;
        });
        
        document.getElementById('wifi-ssid').addEventListener('input', (e) => {
            this.config.stationMode.ssid = e.target.value;
        });
        
        document.getElementById('wifi-password').addEventListener('input', (e) => {
            this.config.stationMode.password = e.target.value;
        });
        
        document.getElementById('server-port').addEventListener('input', (e) => {
            this.config.serverPort = parseInt(e.target.value) || 80;
        });
        
        // Reset button
        document.getElementById('reset-wifi-btn').addEventListener('click', () => {
            this.resetToDefaults();
        });
        
        // Test configuration button
        document.getElementById('test-wifi-btn').addEventListener('click', () => {
            this.testConfiguration();
        });
    }
    
    loadConfig() {
        // Load from localStorage if available
        const savedConfig = localStorage.getItem('wifiConfig');
        if (savedConfig) {
            try {
                this.config = { ...this.config, ...JSON.parse(savedConfig) };
            } catch (error) {
                console.error('Error loading WiFi config:', error);
            }
        }
        
        this.updateUI();
    }
    
    saveConfig() {
        localStorage.setItem('wifiConfig', JSON.stringify(this.config));
    }
    
    updateUI() {
        document.getElementById('ap-ssid').value = this.config.apMode.ssid;
        document.getElementById('ap-password').value = this.config.apMode.password;
        document.getElementById('enable-station-mode').checked = this.config.stationMode.enabled;
        document.getElementById('wifi-ssid').value = this.config.stationMode.ssid;
        document.getElementById('wifi-password').value = this.config.stationMode.password;
        document.getElementById('server-port').value = this.config.serverPort;
        
        // Update disabled state
        const wifiInputs = document.querySelectorAll('#wifi-ssid, #wifi-password');
        wifiInputs.forEach(input => {
            input.disabled = !this.config.stationMode.enabled;
        });
    }
    
    resetToDefaults() {
        this.config = {
            apMode: {
                ssid: 'ESP_AutomationHub',
                password: '12345678'
            },
            stationMode: {
                enabled: false,
                ssid: '',
                password: ''
            },
            serverPort: 80
        };
        
        this.updateUI();
        this.saveConfig();
        this.app.showNotification('WiFi configuration reset to defaults');
    }
    
    testConfiguration() {
        const errors = this.validateConfiguration();
        
        if (errors.length > 0) {
            this.app.showNotification('Configuration errors: ' + errors.join(', '), 'error');
            return;
        }
        
        this.saveConfig();
        this.app.showNotification('WiFi configuration is valid and saved');
    }
    
    validateConfiguration() {
        const errors = [];
        
        // Validate AP mode
        if (!this.config.apMode.ssid || this.config.apMode.ssid.trim().length === 0) {
            errors.push('AP SSID cannot be empty');
        }
        
        if (this.config.apMode.password.length < 8) {
            errors.push('AP password must be at least 8 characters');
        }
        
        // Validate station mode if enabled
        if (this.config.stationMode.enabled) {
            if (!this.config.stationMode.ssid || this.config.stationMode.ssid.trim().length === 0) {
                errors.push('WiFi SSID cannot be empty when station mode is enabled');
            }
            
            if (!this.config.stationMode.password || this.config.stationMode.password.length === 0) {
                errors.push('WiFi password cannot be empty when station mode is enabled');
            }
        }
        
        // Validate server port
        if (this.config.serverPort < 1 || this.config.serverPort > 65535) {
            errors.push('Server port must be between 1 and 65535');
        }
        
        return errors;
    }
    
    getConfig() {
        return this.config;
    }
    
    generateWiFiCode(selectedBoard) {
        let code = '';
        
        // WiFi credentials
        code += '// WiFi Configuration\n';
        code += `const char* ap_ssid = "${this.config.apMode.ssid}";\n`;
        code += `const char* ap_password = "${this.config.apMode.password}";\n`;
        
        if (this.config.stationMode.enabled) {
            code += `const char* wifi_ssid = "${this.config.stationMode.ssid}";\n`;
            code += `const char* wifi_password = "${this.config.stationMode.password}";\n`;
        }
        
        code += '\n';
        
        // Web server
        if (selectedBoard === 'ESP32') {
            code += `WebServer server(${this.config.serverPort});\n\n`;
        } else {
            code += `ESP8266WebServer server(${this.config.serverPort});\n\n`;
        }
        
        return code;
    }
    
    generateWiFiSetupCode() {
        let code = '';
        
        code += '  // WiFi Setup\n';
        code += '  Serial.println("Setting up WiFi...");\n\n';
        
        if (this.config.stationMode.enabled) {
            // Try to connect to existing WiFi first
            code += '  // Try to connect to existing WiFi\n';
            code += '  WiFi.begin(wifi_ssid, wifi_password);\n';
            code += '  Serial.print("Connecting to WiFi");\n';
            code += '  \n';
            code += '  int attempts = 0;\n';
            code += '  while (WiFi.status() != WL_CONNECTED && attempts < 20) {\n';
            code += '    delay(500);\n';
            code += '    Serial.print(".");\n';
            code += '    attempts++;\n';
            code += '  }\n';
            code += '  \n';
            code += '  if (WiFi.status() == WL_CONNECTED) {\n';
            code += '    Serial.println();\n';
            code += '    Serial.println("WiFi connected!");\n';
            code += '    Serial.print("IP address: ");\n';
            code += '    Serial.println(WiFi.localIP());\n';
            code += '  } else {\n';
            code += '    Serial.println();\n';
            code += '    Serial.println("Failed to connect to WiFi. Starting Access Point...");\n';
            code += '    WiFi.softAP(ap_ssid, ap_password);\n';
            code += '    Serial.print("Access Point IP: ");\n';
            code += '    Serial.println(WiFi.softAPIP());\n';
            code += '  }\n';
        } else {
            // Access Point mode only
            code += '  // Start Access Point\n';
            code += '  WiFi.softAP(ap_ssid, ap_password);\n';
            code += '  Serial.print("Access Point started. IP: ");\n';
            code += '  Serial.println(WiFi.softAPIP());\n';
        }
        
        code += '\n';
        
        return code;
    }
}
