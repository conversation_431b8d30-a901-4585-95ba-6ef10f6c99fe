:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --error-color: #e74c3c;
    --text-color: #333;
    --text-light: #666;
    --background-color: #f5f5f5;
    --card-background: #fff;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

.dark-theme {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --error-color: #e74c3c;
    --text-color: #f5f5f5;
    --text-light: #bbb;
    --background-color: #222;
    --card-background: #333;
    --border-color: #444;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: var(--transition);
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.theme-toggle {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.theme-toggle:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.tabs {
    display: flex;
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    padding: 0 2rem;
}

.tab-btn {
    padding: 1rem 2rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--text-light);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

main {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Components Panel */
.components-panel {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 1.5rem;
}

.components-panel h2 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.component-adder {
    margin-bottom: 1.5rem;
}

.add-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.add-btn:hover {
    background-color: var(--primary-dark);
}

.add-btn.small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.components-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.component-card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    transition: var(--transition);
}

.component-card:hover {
    box-shadow: var(--shadow);
}

.component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.component-name {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.component-name i {
    color: var(--primary-color);
}

.component-actions {
    display: flex;
    gap: 0.5rem;
}

.component-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition);
}

.component-actions button:hover {
    color: var(--primary-color);
    background-color: rgba(0, 0, 0, 0.05);
}

.component-details {
    font-size: 0.9rem;
    color: var(--text-light);
}

.component-detail {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    padding: 0.25rem 0;
}

.component-detail:last-child {
    border-bottom: none;
}

/* Automation panel */
.automation-panel {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 1.5rem;
}

.automation-panel h2 {
    margin-bottom: 1rem;
}

.rules-list {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.rule-card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    transition: var(--transition);
}

.rule-card:hover {
    box-shadow: var(--shadow);
}

.rule-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.rule-condition, .rule-actions {
    margin-bottom: 0.75rem;
}

.rule-condition h3, .rule-actions h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.condition-text, .action-text {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.5rem;
    border-radius: 4px;
    font-family: monospace;
}

.rule-card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1rem;
}

/* Code Panel */
.code-panel {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 1.5rem;
}

.code-panel h2 {
    margin-bottom: 1rem;
}

.code-actions {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.primary-btn:hover {
    background-color: var(--primary-dark);
}

.secondary-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.secondary-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.code-preview {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.code-tabs {
    display: flex;
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
}

.code-tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
}

.code-tab-btn:hover {
    color: var(--primary-color);
}

.code-tab-btn.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    background-color: var(--card-background);
}

.code-container {
    display: none;
    padding: 1rem;
    white-space: pre-wrap;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9rem;
    color: var(--text-color);
    max-height: 500px;
    overflow-y: auto;
}

.code-container.active {
    display: block;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--card-background);
    border-radius: 8px;
    width: 95%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 2rem;
    position: relative;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.close:hover {
    color: var(--error-color);
}

.component-types {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.component-type-btn {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: none;
    cursor: pointer;
    transition: var(--transition);
}

.component-type-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.component-type-btn.active {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.component-selection {
    margin-bottom: 1.5rem;
}

.component-group {
    display: none;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.component-group.active {
    display: grid;
}

.component-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.component-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.component-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.component-form {
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
}

.component-form.hidden {
    display: none;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--card-background);
    color: var(--text-color);
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Rule Form */
.condition-row, .action-row {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    margin-bottom: 0.75rem;
}

.action-row {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.02);
}

.duration-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.hidden {
    display: none;
}

#action-list {
    margin-bottom: 1rem;
}

.remove-action-btn {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
}

.remove-action-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

.remove-action-btn:disabled {
    color: var(--text-light);
    cursor: not-allowed;
}

/* Footer */
footer {
    background-color: var(--card-background);
    padding: 1rem 2rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

footer p {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Responsive */
@media (max-width: 768px) {
    .components-list {
        grid-template-columns: 1fr;
    }
    
    .component-group {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .code-actions {
        flex-direction: column;
    }
    
    .code-actions button {
        width: 100%;
    }
    
    .tab-btn {
        padding: 0.75rem 1rem;
    }
    
    .condition-row, .action-row {
        flex-wrap: wrap;
    }
    
    footer {
        flex-direction: column;
        gap: 1rem;
    }
} 