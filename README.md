# ESP8266 Home Automation Designer

A web-based visual designer for ESP8266 home automation projects with code generation capabilities.

## Features

- **Visual Component Design**: Add and configure various sensors and actuators
- **Automation Rules Builder**: Create IF-THEN logic rules
- **Code Generation**: Generate ready-to-use Arduino code and web interface
- **Project Saving**: Save and load your designs

## Supported Components

### Output Devices
- Relay
- PWM
- Fan
- Buzzer
- Water Pump
- Servo Motor
- LED Strip

### Sensors
- DHT11 (Temperature/Humidity)
- Rain Sensor
- LDR (Light Sensor)
- PIR (Motion Sensor)
- Ultrasonic Distance Sensor
- Soil Moisture Sensor
- Flame Sensor
- Gas Sensor (MQ-2)
- IR Receiver
- Sound Sensor

## Getting Started

1. Open `index.html` in a web browser
2. Add components in the Designer tab
3. Create automation rules in the Automation tab
4. Generate and download the code in the Code tab

## Generated Code Features

- WiFi Access Point setup
- Web server with control interface
- Sensor readings
- Automation logic
- Non-blocking code using millis()

## Usage Example

1. Add a DHT11 sensor and a Relay
2. Create a rule: IF Temperature > 30 THEN turn ON Fan
3. Generate the code
4. Upload to your ESP8266
5. Connect to the ESP8266 WiFi network
6. Control your devices through the web interface

## Requirements

- Modern web browser with JavaScript enabled
- ESP8266 board (for deploying the generated code)
- Arduino IDE (for uploading the generated code)

## License

MIT License 